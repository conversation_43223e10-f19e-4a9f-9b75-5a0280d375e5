# 日志目录

这个目录用于存放量化程序的所有日志文件。

## 📋 日志文件说明

### 主程序日志
- `main.log` - 主程序运行日志

### 监控日志
- `monitor1_volume.log` - 成交量监控日志
- `monitor2_pressure.log` - 买卖压力监控日志
- `monitor3_divergence.log` - 背离监控日志
- `monitor4_oi.log` - 持仓量监控日志
- `monitor5_ema_ma_crossover.log` - EMA/MA交叉监控日志
- `monitor6_volume_oi_matrix.log` - 成交量持仓量矩阵监控日志
- `monitor7_bollinger_squeeze.log` - 布林带挤压监控日志

### 交易日志
- `trader1_ema.log` - EMA交易日志
- `trader2_sentiment.log` - 情绪交易日志
- `trader3_orderbook.log` - 订单簿交易日志

### 工具脚本日志
- `reload_config.log` - 配置重载脚本日志
- `test.log` - 测试脚本日志

## 🔧 日志配置

- **文件大小限制**: 5MB
- **备份文件数量**: 30个
- **日志级别**: 
  - 控制台: INFO及以上
  - 文件: DEBUG及以上
- **日志格式**: `YYYY-MM-DD HH:MM:SS - LEVEL - MESSAGE`

## 📊 日志轮转

当日志文件达到5MB时，会自动轮转：
- `main.log` → `main-1.log`
- `main-1.log` → `main-2.log`
- ...
- 最多保留30个备份文件

## 🔍 查看日志

### 使用管理脚本
```bash
# 查看最新日志
./quant.sh logs

# 实时查看日志
./quant.sh tail
```

### 直接查看文件
```bash
# 查看主程序日志
tail -f logs/main.log

# 查看特定监控日志
tail -f logs/monitor1_volume.log

# 查看所有日志
tail -f logs/*.log
```

## 📝 日志文件命名规范

项目中所有模块都遵循统一的日志文件命名规范：
- **格式**: `{模块名}.log`
- **示例**: `main.py` → `main.log`
- **强制要求**: `setup_logger()` 函数要求必须明确指定日志文件名，不提供默认值
- **避免产生**: 不会产生任何默认日志文件，确保每个日志文件都有明确的用途

## 📝 注意事项

1. 日志文件会自动创建，无需手动创建
2. 日志文件已添加到 `.gitignore`，不会提交到版本控制
3. 定期清理旧的日志文件以节省磁盘空间
4. 重要的错误信息会同时输出到控制台和日志文件
5. 每个模块都应使用独立的日志文件，便于问题定位和调试
