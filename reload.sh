#!/bin/bash
# 配置重载快捷脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 脚本路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
RELOAD_SCRIPT="$SCRIPT_DIR/scripts/reload_config.py"

# 检查重载脚本是否存在
if [ ! -f "$RELOAD_SCRIPT" ]; then
    echo -e "${RED}❌ 重载脚本不存在: $RELOAD_SCRIPT${NC}"
    exit 1
fi

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🔧 配置重载快捷脚本${NC}"
    echo "用法: $0 [选项] [配置类型]"
    echo ""
    echo "配置类型:"
    echo "  all       重载所有配置 (默认)"
    echo "  app       重载应用配置"
    echo "  monitor   重载监控配置"
    echo "  exchange  重载交易所配置"
    echo "  telegram  重载Telegram配置"
    echo "  trader    重载交易配置"
    echo ""
    echo "选项:"
    echo "  -s, --show     显示当前配置"
    echo "  -h, --help     显示此帮助信息"
    echo "  --no-reload    只显示配置，不重载"
    echo ""
    echo "示例:"
    echo "  $0                    # 重载所有配置"
    echo "  $0 monitor            # 重载监控配置"
    echo "  $0 -s                 # 显示所有配置"
    echo "  $0 monitor -s         # 显示监控配置并重载"
    echo "  $0 --no-reload -s     # 只显示配置，不重载"
}

# 解析参数
CONFIG_TYPE="all"
SHOW_CONFIG=false
NO_RELOAD=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -s|--show)
            SHOW_CONFIG=true
            shift
            ;;
        --no-reload)
            NO_RELOAD=true
            shift
            ;;
        app|monitor|exchange|telegram|trader|all)
            CONFIG_TYPE="$1"
            shift
            ;;
        *)
            echo -e "${RED}❌ 未知参数: $1${NC}"
            echo "使用 $0 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 构建Python命令
PYTHON_CMD="python $RELOAD_SCRIPT -t $CONFIG_TYPE"

if [ "$SHOW_CONFIG" = true ]; then
    PYTHON_CMD="$PYTHON_CMD -s"
fi

if [ "$NO_RELOAD" = true ]; then
    PYTHON_CMD="$PYTHON_CMD --no-reload"
fi

# 显示执行的命令
echo -e "${BLUE}🔄 执行配置重载...${NC}"
echo -e "${YELLOW}命令: $PYTHON_CMD${NC}"
echo ""

# 执行命令
if eval "$PYTHON_CMD"; then
    echo ""
    echo -e "${GREEN}✅ 配置重载完成${NC}"
else
    echo ""
    echo -e "${RED}❌ 配置重载失败${NC}"
    exit 1
fi
