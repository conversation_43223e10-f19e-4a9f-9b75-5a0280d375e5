#!/usr/bin/env python3
"""
展示其他优雅的程序停止方式

这个示例演示了6种不同的程序停止方法：
1. asyncio.Event - 当前量化程序采用的方法
2. threading.Event - 多线程环境
3. Queue通信 - 消息传递方式
4. 信号处理 - 系统级控制
5. 多进程Event - 多进程架构
6. 共享变量 - 简单状态控制

运行方式：
    python docs/examples/alternative_stop_methods.py
"""

import asyncio
import signal
import sys
import threading
import queue
import time
from multiprocessing import Process, Event as MPEvent, Queue as MPQueue

class StopMethodDemo:
    """停止方法演示类"""
    
    def __init__(self):
        self.running = True
    
    async def demo_asyncio_event(self):
        """方式1: asyncio.Event (当前采用)"""
        print("🎯 方式1: asyncio.Event")
        print("优点: 响应快速，代码简洁，内存操作")
        print("缺点: 仅限单进程内通信")
        
        stop_event = asyncio.Event()
        
        async def worker():
            for i in range(10):
                if stop_event.is_set():
                    print("🛑 Worker收到停止信号")
                    break
                print(f"⚙️ Worker运行中... {i+1}/10")
                await asyncio.sleep(0.5)
        
        async def stop_trigger():
            await asyncio.sleep(3)
            print("📱 3秒后触发停止")
            stop_event.set()
        
        await asyncio.gather(worker(), stop_trigger())
        print("✅ asyncio.Event演示完成\n")
    
    def demo_threading_event(self):
        """方式2: threading.Event"""
        print("🎯 方式2: threading.Event")
        print("优点: 线程间通信，响应快速")
        print("缺点: 仅限单进程内，需要线程管理")
        
        stop_event = threading.Event()
        
        def worker():
            for i in range(10):
                if stop_event.is_set():
                    print("🛑 Worker线程收到停止信号")
                    break
                print(f"⚙️ Worker线程运行中... {i+1}/10")
                time.sleep(0.5)
        
        def stop_trigger():
            time.sleep(3)
            print("📱 3秒后触发停止")
            stop_event.set()
        
        # 启动线程
        worker_thread = threading.Thread(target=worker)
        trigger_thread = threading.Thread(target=stop_trigger)
        
        worker_thread.start()
        trigger_thread.start()
        
        worker_thread.join()
        trigger_thread.join()
        print("✅ threading.Event演示完成\n")
    
    def demo_queue_communication(self):
        """方式3: Queue通信"""
        print("🎯 方式3: Queue通信")
        print("优点: 可传递复杂数据，支持多线程")
        print("缺点: 相对复杂，需要轮询")
        
        stop_queue = queue.Queue()
        
        def worker():
            for i in range(10):
                try:
                    # 非阻塞检查队列
                    message = stop_queue.get_nowait()
                    if message == "STOP":
                        print(f"🛑 Worker收到停止消息: {message}")
                        break
                except queue.Empty:
                    pass
                
                print(f"⚙️ Worker运行中... {i+1}/10")
                time.sleep(0.5)
        
        def stop_trigger():
            time.sleep(3)
            print("📱 3秒后发送停止消息")
            stop_queue.put("STOP")
        
        # 启动线程
        worker_thread = threading.Thread(target=worker)
        trigger_thread = threading.Thread(target=stop_trigger)
        
        worker_thread.start()
        trigger_thread.start()
        
        worker_thread.join()
        trigger_thread.join()
        print("✅ Queue通信演示完成\n")
    
    def demo_signal_handling(self):
        """方式4: 信号处理"""
        print("🎯 方式4: 信号处理")
        print("优点: 系统级支持，跨进程，标准做法")
        print("缺点: 平台相关，信号处理复杂")
        print("💡 按Ctrl+C测试信号处理")
        
        def signal_handler(signum, frame):
            print(f"\n🛑 收到信号 {signum}")
            self.running = False
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            for i in range(10):
                if not self.running:
                    print("🛑 程序收到停止信号")
                    break
                print(f"⚙️ 程序运行中... {i+1}/10")
                time.sleep(0.5)
        except KeyboardInterrupt:
            print("\n🛑 键盘中断")
        
        print("✅ 信号处理演示完成\n")
        self.running = True  # 重置状态
    
    def demo_multiprocess_event(self):
        """方式5: 多进程Event"""
        print("🎯 方式5: 多进程Event")
        print("优点: 支持多进程通信，高性能")
        print("缺点: 实现复杂，需要进程管理")
        
        def worker_process(stop_event, process_id):
            for i in range(10):
                if stop_event.is_set():
                    print(f"🛑 进程{process_id}收到停止信号")
                    break
                print(f"⚙️ 进程{process_id}运行中... {i+1}/10")
                time.sleep(0.5)
        
        # 创建多进程事件
        stop_event = MPEvent()
        
        # 启动工作进程
        process = Process(target=worker_process, args=(stop_event, 1))
        process.start()
        
        # 3秒后触发停止
        time.sleep(3)
        print("📱 3秒后触发多进程停止")
        stop_event.set()
        
        process.join()
        print("✅ 多进程Event演示完成\n")
    
    def demo_shared_variable(self):
        """方式6: 共享变量 (简单但不推荐)"""
        print("🎯 方式6: 共享变量")
        print("优点: 实现简单")
        print("缺点: 线程安全问题，不适合复杂场景")
        
        class SharedState:
            def __init__(self):
                self.should_stop = False
                self._lock = threading.Lock()
            
            def stop(self):
                with self._lock:
                    self.should_stop = True
            
            def is_stopped(self):
                with self._lock:
                    return self.should_stop
        
        shared_state = SharedState()
        
        def worker():
            for i in range(10):
                if shared_state.is_stopped():
                    print("🛑 Worker检测到停止标志")
                    break
                print(f"⚙️ Worker运行中... {i+1}/10")
                time.sleep(0.5)
        
        def stop_trigger():
            time.sleep(3)
            print("📱 3秒后设置停止标志")
            shared_state.stop()
        
        # 启动线程
        worker_thread = threading.Thread(target=worker)
        trigger_thread = threading.Thread(target=stop_trigger)
        
        worker_thread.start()
        trigger_thread.start()
        
        worker_thread.join()
        trigger_thread.join()
        print("✅ 共享变量演示完成\n")

def main():
    demo = StopMethodDemo()
    
    print("=" * 60)
    print("🚀 程序停止方式演示")
    print("=" * 60)
    
    methods = [
        ("asyncio.Event (推荐)", demo.demo_asyncio_event),
        ("threading.Event", demo.demo_threading_event),
        ("Queue通信", demo.demo_queue_communication),
        ("信号处理", demo.demo_signal_handling),
        ("多进程Event", demo.demo_multiprocess_event),
        ("共享变量", demo.demo_shared_variable),
    ]
    
    print("选择要演示的方式:")
    for i, (name, _) in enumerate(methods, 1):
        print(f"{i}. {name}")
    print("7. 全部演示")
    print("8. 退出")
    
    try:
        choice = int(input("\n请选择 (1-8): ").strip())
        
        if choice == 8:
            print("退出演示")
            return
        elif choice == 7:
            print("🎬 开始全部演示...\n")
            for name, method in methods:
                print(f"▶️ 演示: {name}")
                if asyncio.iscoroutinefunction(method):
                    asyncio.run(method())
                else:
                    method()
                input("按Enter继续下一个演示...")
        elif 1 <= choice <= 6:
            name, method = methods[choice - 1]
            print(f"▶️ 演示: {name}\n")
            if asyncio.iscoroutinefunction(method):
                asyncio.run(method())
            else:
                method()
        else:
            print("无效选择")
    
    except ValueError:
        print("请输入有效数字")
    except KeyboardInterrupt:
        print("\n\n🛑 演示被中断")

if __name__ == "__main__":
    main()
