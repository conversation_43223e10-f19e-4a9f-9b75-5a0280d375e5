# 示例代码目录

这个目录包含了各种示例代码和演示脚本，用于学习和参考。

## 📋 示例列表

### 🛑 程序停止方法演示
- `alternative_stop_methods.py` - 各种程序停止方法的演示
  - asyncio.Event（当前采用）
  - threading.Event
  - Queue通信
  - 信号处理
  - 多进程Event
  - 共享变量

## 🚀 使用方法

### 程序停止方法演示
```bash
# 从项目根目录运行
python docs/examples/alternative_stop_methods.py

# 选择要演示的方法
# 1. asyncio.Event (推荐)
# 2. threading.Event
# 3. Queue通信
# 4. 信号处理
# 5. 多进程Event
# 6. 共享变量
# 7. 全部演示
# 8. 退出
```

## 📚 学习价值

### 停止方法对比
| 方法 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| asyncio.Event | 响应快速，代码简洁 | 仅限单进程 | 异步程序（推荐） |
| threading.Event | 线程间通信快速 | 需要线程管理 | 多线程程序 |
| Queue通信 | 可传递复杂数据 | 相对复杂 | 需要传递停止信息 |
| 信号处理 | 系统级支持，标准做法 | 平台相关 | 生产环境 |
| 多进程Event | 支持多进程 | 实现复杂 | 多进程架构 |
| 共享变量 | 实现简单 | 线程安全问题 | 简单场景 |

## 🎯 设计思路

这些示例展示了不同的程序控制模式：

1. **事件驱动**：使用Event对象进行状态同步
2. **消息传递**：通过Queue传递控制消息
3. **信号机制**：利用操作系统信号
4. **状态共享**：通过共享变量控制

## 📝 注意事项

1. 这些是演示代码，主要用于学习和理解
2. 实际项目中建议使用asyncio.Event（当前采用）
3. 运行演示时可以按Ctrl+C测试信号处理
4. 某些演示可能需要用户交互

## 🔗 相关文档

- 主程序使用的是asyncio.Event方法
- 详细的停止机制请参考主程序代码
- 更多设计模式请参考项目文档
