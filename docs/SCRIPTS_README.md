# 量化程序管理脚本使用说明

## 📋 脚本概览

本项目提供了完整的程序管理脚本，支持启动、停止、重启、状态查询等功能。

### 🔧 脚本文件

| 脚本文件 | 平台 | 功能 |
|----------|------|------|
| `quant.sh` | macOS/Linux | 一站式管理工具（推荐） |
| `scripts/start_quant.sh` | macOS/Linux | 启动程序 |
| `scripts/stop_quant.sh` | macOS/Linux | 停止程序 |
| `scripts/status_quant.sh` | macOS/Linux | 状态查询 |
| `scripts/start_quant.bat` | Windows | 启动程序 |
| `scripts/stop_quant.bat` | Windows | 停止程序 |

## 🚀 快速开始

### macOS/Linux 用户（推荐）

```bash
# 一站式管理工具
./quant.sh help              # 查看帮助
./quant.sh start             # 启动程序
./quant.sh start caffeinate  # 使用caffeinate启动（防休眠）
./quant.sh status            # 查看状态
./quant.sh ps                # 查看相关进程
./quant.sh stop              # 停止程序
./quant.sh restart           # 重启程序（安全重启，确保资源清理）
./quant.sh restart caffeinate # 使用caffeinate重启

# 配置管理
./quant.sh reload             # 重载所有配置
./quant.sh reload monitor     # 重载监控配置
./quant.sh config             # 显示当前配置
```

### Windows 用户

```cmd
start_quant.bat              # 启动程序
stop_quant.bat               # 停止程序
stop_quant.bat force         # 强制停止
```

## 📖 详细使用说明

### 🚀 启动程序

#### macOS/Linux
```bash
# 普通启动
./quant.sh start
# 或
./start_quant.sh

# 使用caffeinate启动（防止Mac休眠）
./quant.sh start caffeinate
# 或
./start_quant.sh caffeinate
```

#### Windows
```cmd
start_quant.bat
```

### 🛑 停止程序

#### macOS/Linux
```bash
# 优雅停止（推荐）
./quant.sh stop
# 或
./stop_quant.sh

# 强制停止
./quant.sh stop force
# 或
./stop_quant.sh force

# 通过Telegram停止
./quant.sh stop telegram
# 或
./stop_quant.sh telegram
```

#### Windows
```cmd
# 优雅停止
stop_quant.bat

# 强制停止
stop_quant.bat force

# 通过Telegram停止
stop_quant.bat telegram
```

### 🔄 重启程序

#### macOS/Linux
```bash
# 普通重启
./quant.sh restart

# 使用caffeinate重启
./quant.sh restart caffeinate
```

#### Windows
重启功能通过Telegram远程控制实现，发送 `/restart` 命令。

### 📊 状态查询

#### macOS/Linux
```bash
# 查看程序状态
./quant.sh status
# 或
./status_quant.sh

# 查看相关进程
./quant.sh ps
# 或
./status_quant.sh ps

# 查看最新日志
./quant.sh logs
# 或
./status_quant.sh logs

# 实时查看日志
./quant.sh tail
# 或
./status_quant.sh tail
```

### ⚙️ 配置管理

#### macOS/Linux
```bash
# 重载所有配置
./quant.sh reload

# 重载特定配置
./quant.sh reload app          # 重载应用配置
./quant.sh reload monitor      # 重载监控配置
./quant.sh reload exchange     # 重载交易所配置
./quant.sh reload telegram     # 重载Telegram配置
./quant.sh reload trader       # 重载交易配置

# 显示当前配置
./quant.sh config
```

**配置类型说明**:
- `app`: 应用主配置（任务启用、内存监控等）
- `monitor`: 监控配置（阈值、参数等）
- `exchange`: 交易所配置（API限制、重试等）
- `telegram`: Telegram配置（命令、回复等）
- `trader`: 交易配置（风险管理、参数等）

## 📱 Telegram 远程控制

### 支持的命令

| 命令 | 功能 |
|------|------|
| `/start`, `启动程序` | 启动程序 |
| `/stop`, `停止程序` | 停止程序 |
| `/restart`, `重启程序` | 重启程序 |
| `/status`, `状态` | 查询状态 |

### 使用示例

```
用户: /restart
机器人: 🔄 收到重启指令，量化程序即将重启...

用户: /status
机器人: ✅ 量化程序运行正常，所有任务正在执行中

用户: /stop
机器人: 🛑 收到停止指令，量化程序即将关闭...
```

## 🔧 高级功能

### caffeinate 支持（macOS）

`caffeinate` 是macOS的系统工具，可以防止系统休眠：

```bash
# 使用caffeinate启动
./quant.sh start caffeinate

# 重启时会自动保持caffeinate方式
# 在Telegram中发送 /restart，程序会使用caffeinate重启
```

### 进程锁机制

- 程序使用进程锁确保只有一个实例运行
- 重启时会智能等待旧进程退出
- 支持检测僵尸进程并自动清理

### 日志管理

```bash
# 查看主程序日志
./quant.sh logs

# 实时跟踪日志
./quant.sh tail

# 查看特定日志文件
tail -f logs/corsair_engine.log
```

## 🛠️ 故障排除

### 常见问题

1. **权限问题**
   ```bash
   chmod +x *.sh  # 给脚本执行权限
   ```

2. **程序无法停止**
   ```bash
   ./quant.sh stop force  # 强制停止
   ```

3. **重启失败**
   ```bash
   # 手动停止后重新启动
   ./quant.sh stop
   sleep 5
   ./quant.sh start
   ```

4. **查看详细状态**
   ```bash
   ./quant.sh status  # 查看详细状态信息
   ./quant.sh ps      # 查看相关进程
   ```

5. **手动查看进程**
   ```bash
   # 查看量化程序相关进程
   ps aux | grep python | grep -E "(main\.py|Quant)" | grep -v grep

   # 查看所有Python进程
   ps aux | grep python
   ```

### 日志位置

所有日志文件都存放在 `logs/` 目录下：

- 主程序日志: `logs/corsair_engine.log`
- 监控日志:
  - `logs/monitor1_volume.log`
  - `logs/monitor2_pressure.log`
  - `logs/monitor3_divergence.log`
  - `logs/monitor4_oi.log`
  - `logs/monitor5_ema_ma_crossover.log`
  - `logs/monitor6_volume_oi_matrix.log`
  - `logs/monitor7_bollinger_squeeze.log`
- 交易日志:
  - `logs/trader1_ema.log`
  - `logs/trader2_sentiment.log`
  - `logs/trader3_orderbook.log`

## 📝 注意事项

1. **首次使用**：确保脚本有执行权限
2. **caffeinate**：仅在macOS上可用
3. **Telegram配置**：确保Telegram机器人已正确配置
4. **网络环境**：确保网络连接正常
5. **API密钥**：确保交易所API密钥已正确设置

## 🎯 最佳实践

1. **推荐使用一站式管理工具**：`./quant.sh`
2. **生产环境使用caffeinate**：防止系统休眠
3. **定期查看日志**：`./quant.sh logs`
4. **使用Telegram监控**：远程查看程序状态
5. **优雅停止**：避免使用强制停止
