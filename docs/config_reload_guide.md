# 配置热重载使用指南

本指南介绍如何在不重启程序的情况下重载配置文件。

## 🔄 支持的重载方式

### 方式1：Telegram远程重载（推荐）

通过Telegram群组发送命令来重载配置，无需登录服务器。

**支持的命令：**

| 命令 | 功能 | 示例 |
|------|------|------|
| `/reload` | 重载所有配置 | `/reload` |
| `重载配置` | 重载所有配置 | `重载配置` |
| `/reload_monitor` | 重载监控配置 | `/reload_monitor` |
| `重载监控配置` | 重载监控配置 | `重载监控配置` |
| `/reload_app` | 重载应用配置 | `/reload_app` |
| `重载应用配置` | 重载应用配置 | `重载应用配置` |
| `/reload_trader` | 重载交易配置 | `/reload_trader` |
| `重载交易配置` | 重载交易配置 | `重载交易配置` |
| `/reload_exchange` | 重载交易所配置 | `/reload_exchange` |
| `重载交易所配置` | 重载交易所配置 | `重载交易所配置` |

**使用步骤：**
1. 修改配置文件（如 `config/monitor_config.yaml`）
2. 在Telegram群组中发送重载命令
3. 查看程序日志确认重载成功

### 方式2：重载脚本

使用专门的重载脚本来重载配置。

**基本用法：**
```bash
# 重载所有配置
python scripts/reload_config.py

# 重载特定配置
python scripts/reload_config.py -t monitor    # 重载监控配置
python scripts/reload_config.py -t app        # 重载应用配置
python scripts/reload_config.py -t exchange   # 重载交易所配置
python scripts/reload_config.py -t telegram   # 重载Telegram配置
python scripts/reload_config.py -t trader     # 重载交易配置

# 显示当前配置
python scripts/reload_config.py -s            # 显示所有配置
python scripts/reload_config.py -t monitor -s # 显示监控配置

# 只显示配置，不重载
python scripts/reload_config.py --no-reload -s
```

**脚本参数说明：**
- `-t, --type`: 指定配置类型 (app/monitor/exchange/telegram/trader/all)
- `-s, --show`: 显示当前配置状态
- `--no-reload`: 只显示配置，不执行重载

## 📋 配置文件说明

### 支持重载的配置文件

| 配置文件 | 类型标识 | 说明 |
|----------|----------|------|
| `config/app_config.yaml` | `app` | 应用主配置 |
| `config/monitor_config.yaml` | `monitor` | 监控模块配置 |
| `config/exchange_config.yaml` | `exchange` | 交易所配置 |
| `config/telegram_config.yaml` | `telegram` | Telegram配置 |
| `config/trader_config.yaml` | `trader` | 交易策略配置 |

### 常见配置修改场景

**1. 调整监控阈值**
```yaml
# config/monitor_config.yaml
monitor:
  volume:
    threshold_multiplier: 3.0  # 修改成交量阈值
  pressure:
    strong_buy_threshold: 75.0  # 修改强买压阈值
```

**2. 启用/禁用监控任务**
```yaml
# config/app_config.yaml
app:
  tasks:
    enabled_monitors:
      - "monitor1_volume"
      - "monitor2_pressure"
      # - "monitor3_divergence"  # 注释掉禁用
```

**3. 调整内存监控**
```yaml
# config/app_config.yaml
app:
  memory:
    warning_threshold: 85      # 内存警告阈值
    critical_threshold: 92     # 内存严重阈值
```

## 🧪 测试配置重载

运行测试脚本验证重载功能：

```bash
python tests/test_config_reload.py
```

测试步骤：
1. 选择"基本重载测试"
2. 按提示修改配置文件
3. 按回车键执行重载
4. 查看配置变化

## ⚠️ 注意事项

### 1. 配置语法检查
- 确保YAML文件语法正确
- 注意缩进和数据类型
- 修改前建议备份原文件

### 2. 重载影响范围
- **立即生效**：阈值参数、开关配置等
- **需要重启任务**：新增/删除监控任务
- **需要重启程序**：交易所连接配置等

### 3. 错误处理
- 配置语法错误会导致重载失败
- 重载失败时会保持原配置
- 查看日志文件了解详细错误信息

## 🔧 推荐的配置修改流程

1. **备份配置**
   ```bash
   cp config/monitor_config.yaml config/monitor_config.yaml.bak
   ```

2. **修改配置文件**
   ```bash
   vim config/monitor_config.yaml
   ```

3. **验证语法**
   ```bash
   python -c "import yaml; yaml.safe_load(open('config/monitor_config.yaml'))"
   ```

4. **重载配置**
   - Telegram: 发送 `/reload_monitor`
   - 脚本: `python scripts/reload_config.py -t monitor`

5. **验证生效**
   ```bash
   python scripts/reload_config.py -t monitor -s
   ```

## 📝 常见问题

**Q: 重载后配置没有生效？**
A: 检查配置文件语法，查看程序日志确认重载是否成功。

**Q: 可以重载哪些配置？**
A: 大部分运行时参数都可以重载，但交易所连接等底层配置可能需要重启程序。

**Q: 重载会影响正在运行的任务吗？**
A: 一般不会中断任务，但新配置会在下次执行时生效。

**Q: 如何确认重载成功？**
A: 查看程序日志或使用 `-s` 参数显示当前配置状态。

## 🎯 最佳实践

1. **使用Telegram重载**：远程操作方便，有操作记录
2. **小步修改**：一次只修改少量配置，便于排查问题
3. **及时验证**：修改后立即验证配置是否生效
4. **保留备份**：重要配置修改前先备份
5. **监控日志**：关注程序日志中的配置重载信息
