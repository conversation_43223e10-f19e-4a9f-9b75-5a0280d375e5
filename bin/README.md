# 可执行文件目录

这个目录包含了程序的可执行文件和符号链接。

## 📋 文件列表

- `quant` - 主程序入口的符号链接，指向 `../quant.sh`

## 🚀 使用方法

### 添加到 PATH（可选）
```bash
# 将bin目录添加到PATH环境变量
export PATH="$PWD/bin:$PATH"

# 然后可以在任何地方使用
quant start caffeinate
quant status
quant stop
```

### 直接使用
```bash
# 直接使用符号链接
./bin/quant start caffeinate
./bin/quant status
./bin/quant stop
```

## 📝 说明

这个目录遵循Unix/Linux的标准目录结构约定：
- `bin/` 目录通常用于存放可执行文件
- 通过符号链接的方式，提供统一的程序入口
- 便于将程序添加到系统PATH中
