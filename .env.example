# 量化交易程序环境变量配置模板
# 使用方法：复制此文件为 .env 并填入真实值
# cp .env.example .env

# ================================
# 交易所API配置（必需）
# ================================
# 在交易所官网申请API密钥，设置适当权限（读取+交易）
# 安全提示：请妥善保管API密钥，建议设置IP白名单
API_KEY=your_api_key_here
API_SECRET=your_api_secret_here

# ================================
# Telegram配置（可选但推荐）
# ================================
# 用于远程监控、控制和接收通知
# 获取方法请参考README.md中的详细说明

# 群组或频道ID（接收消息的目标）
TELEGRAM_CHAT_ID=your_chat_id

# 多Bot配置（推荐）- 不同类型消息使用不同Bot，便于分类管理
TELEGRAM_BOT_TOKEN_QUANT=your_bot_token_quant      # 主程序通知（启动、停止、错误等）
TELEGRAM_BOT_TOKEN_MONITOR=your_bot_token_monitor  # 监控通知（市场数据、技术指标等）
TELEGRAM_BOT_TOKEN_TRADER=your_bot_token_trader # 交易通知（交易信号、订单执行等）

# 单Bot配置（简化版）- 如果只想使用一个Bot，请注释掉上面的配置，启用下面的配置
# TELEGRAM_BOT_TOKEN=your_bot_token
