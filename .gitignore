# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
.hypothesis/
.pytest_cache/
nosetests.xml
unittest-xml-reporting/
test-results/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py

db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/
build/doctrees
build/html

# PyBuilder
target/

# IPython and <PERSON><PERSON>ter Notebook
profile_default/
ipython_config.py
.ipynb_checkpoints
*.nbconvert/

# pyenv
.python-version

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# C extensions
*.so

# Packaging
*.dSYM/
*.dll
*.exe
*.test
*.test.* 
*.tests 
*.tests.*
*.o
*.obj
*.pdb
*.swp
*.map
*.a
*.exp
*.lib 
*.clw
*.layout 
*.command
*.bak 
*.launch 
*.ncb 
*.hpj 
*.res

# Docker
*.env

.DS_Store

# 量化程序相关
logs/*.log
logs/*.log.*
*.lock
