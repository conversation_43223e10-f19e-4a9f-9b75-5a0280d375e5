import ccxt
import pandas as pd
from dotenv import load_dotenv
import os
from datetime import datetime, timedelta
import time

# 加载.env文件, 读取apiKey和secret 【已经添加.env到.gitignore中，所以不会被提交到git中，也就不会泄露】
load_dotenv()
API_KEY = os.getenv("API_KEY")
API_SECRET = os.getenv("API_SECRET")

# 配置Binance交易所
exchange = ccxt.binance({
    'apiKey': API_KEY,
    'secret': API_SECRET,
    'enableRateLimit': True,
    'proxies': {
        'http': 'http://127.0.0.1:7897',  # HTTP 代理地址和端口
        'https': 'http://127.0.0.1:7897',
    }
})

symbol = 'BTC/USDT:USDT'  # 交易对
timeframe = '5m'  # 时间周期
limit = 1000  # 每次最大获取条数

# 指定北京时间
beijing_time = "2025-01-05 00:00:00"
beijing_datetime = datetime.strptime(beijing_time, "%Y-%m-%d %H:%M:%S")

# 转换为 UTC 时间
utc_datetime = beijing_datetime - timedelta(hours=8)
since = int(utc_datetime.timestamp() * 1000)  # 转为毫秒时间戳
# since = exchange.parse8601('2023-01-01T00:00:00Z')  # 获取从2023年1月1日起的数据

# # 获取历史数据
# data = exchange.fetch_ohlcv(symbol, timeframe, since=since, limit=limit)
# df = pd.DataFrame(data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

# # 转换时间戳为日期时间格式
# df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')

# # 保存到CSV文件
# csv_file = 'historical_data.csv'
# df.to_csv(csv_file, index=False)
# print(f"历史数据已保存到 {csv_file}")

exchange.load_markets()

all_data = []  # 存储所有数据
end_time = exchange.milliseconds()  # 当前时间戳（毫秒）

while since < end_time:
    # 获取数据
    ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since=since, limit=limit)
    
    if not ohlcv:
        break  # 如果没有返回数据，结束循环

    all_data.extend(ohlcv)  # 累加数据

    # 更新 since 为最新时间（上一批次的最后一根K线的时间戳 + 时间间隔）
    since = ohlcv[-1][0] + exchange.parse_timeframe(timeframe) * 1000  # 转换为毫秒

    print(f"获取到 {len(ohlcv)} 条数据，最新时间: {datetime.utcfromtimestamp(ohlcv[-1][0] / 1000)}")

    time.sleep(1)

# 转换为 DataFrame
columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
df = pd.DataFrame(all_data, columns=columns)

# 转换时间戳为北京时间
df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms') + timedelta(hours=8)

# 保存为 CSV 文件
df.to_csv("historical_data.csv", index=False)
print("所有数据已保存到 historical_data.csv")