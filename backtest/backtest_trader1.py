"""回测：交易1：暂未完全调试好"""

import pandas as pd
import numpy as np
from datetime import datetime
import logging
from tabulate import tabulate
import math

from utils.utils_common import format_volume, parse_interval, get_sleep_time, format_time, format_dataframe_with_tabulate


# 设置日志记录器
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("BacktestLogger")

# 交易参数
# trade_symbol = "BTC/USDT"
# timeframe = "5m"
ema_period = 20
entry_k_count = 3
leverage = 5
position_size_ratio = 0.5
tick_size_custom = 0.2
initial_funds=10000

# 回测状态
position = None  # 当前持仓状态："LONG", "SHORT", or None
entry_price = 0.0
pnl = 0.0  # 总盈亏
balance = 10000  # 初始账户余额
position_size = 0.0  # 持仓规模
initial_balance = balance
trades = []  # 存储交易记录

# 定义初始 EMA 计算方法
def calculate_ema(data, period, previous_ema=None):
    ema_values = []
    alpha = 2 / (period + 1)

    # 如果未指定 previous_ema，则用前 period 条数据的均值初始化
    if previous_ema is None:
        previous_ema = data['close'].iloc[:period].mean()

    # 初始化 EMA 列表
    ema_values.extend([None] * (period - 1))
    ema_values.append(previous_ema)

    # 从第 period 条数据开始迭代计算
    for price in data['close'].iloc[period:]:
        current_ema = round(alpha * price + (1 - alpha) * previous_ema, 8)  # Binance 精度
        ema_values.append(current_ema)
        previous_ema = current_ema  # 更新 previous_ema

    return pd.Series(ema_values, index=data.index)

def truncate(x, n):
    factor = 10 ** n
    return math.trunc(x * factor) / factor


def backtest(history_data, ema_period, entry_k_count, tick_size_custom, leverage, position_size_ratio, initial_funds):
    """
    严格按照交易逻辑的回测程序，支持挂单、持仓状态、资金管理，并记录撤销挂单。
    支持 leverage 和 position_size_ratio 参数，灵活控制杠杆和开仓比例。
    """
    # 初始化状态
    trades = []  # 交易记录
    pnl = 0  # 总盈亏
    funds = initial_funds  # 初始资金
    has_long_position = False  # 是否持有多头仓位
    has_short_position = False  # 是否持有空头仓位
    has_open_long = False  # 是否有挂单开多
    has_open_short = False  # 是否有挂单开空
    has_open_close_long = False  # 是否有挂单平多
    has_open_close_short = False  # 是否有挂单平空
    long_position_size = 0  # 当前多头仓位大小
    short_position_size = 0  # 当前空头仓位大小
    long_entry_price = 0  # 多头入场价格
    short_entry_price = 0  # 空头入场价格
    last_start_index = None  # 保存上一次计算的起始 K 线索引

    # 遍历历史数据
    for index, row in history_data.iterrows():
        # 寻找 start_index
        start_index = None
        for i in range(index, -1, -1):  # 从当前索引向前查找
            if (
                history_data.at[i, 'low'] <= history_data.at[i, f"ema{ema_period}"] <= history_data.at[i, 'high']
            ):
                start_index = i
                break

        if start_index is None:
            continue  # 没找到 start_index，跳过

        # 检查 start_index 是否发生变化
        if last_start_index != start_index:
            # 如果起始 K 线发生变化，清理未成交挂单（模拟撤销所有挂单）
            if has_open_long:
                trades.append((row['timestamp'], 'CANCEL_OPEN_LONG', None, None))
                print(f"撤销挂多单")
            if has_open_short:
                trades.append((row['timestamp'], 'CANCEL_OPEN_SHORT', None, None))
                print(f"撤销挂空单")
            if has_open_close_long:
                trades.append((row['timestamp'], 'CANCEL_CLOSE_LONG', None, None))
                print(f"撤销平多单挂单")
            if has_open_close_short:
                trades.append((row['timestamp'], 'CANCEL_CLOSE_SHORT', None, None))
                print(f"撤销平空单挂单")
            last_start_index = start_index
            has_open_long = False
            has_open_short = False
            has_open_close_long = False
            has_open_close_short = False

        # 交易逻辑：检查连续 entry_k_count 根 K 线
        if start_index is not None and start_index + entry_k_count < index:  # 确保索引不会越界
            # 检查是否满足多头条件
            if all(
                history_data.loc[start_index + j, 'low']
                > history_data.loc[start_index + j, f"ema{ema_period}"]
                for j in range(1, entry_k_count + 1)
            ):
                trigger_price = max(
                    history_data.loc[start_index + 1:start_index + entry_k_count + 1, 'high']
                ) + tick_size_custom

                # 挂多单
                if not has_long_position and not has_open_long and funds > 0:
                    position_size = funds * position_size_ratio * leverage / trigger_price
                    has_open_long = True
                    trades.append((row['timestamp'], 'OPEN_LONG', trigger_price, position_size))
                    print(f"挂多头触发单: LONG @ {trigger_price}, 仓位: {position_size}")

                # 挂平空单
                if has_short_position and not has_open_close_short:
                    close_price = trigger_price
                    has_open_close_short = True
                    trades.append((row['timestamp'], 'OPEN_CLOSE_SHORT', close_price, short_position_size))
                    print(f"挂平空单: SHORT @ {close_price}, 仓位: {short_position_size}")

            # 检查是否满足空头条件
            elif all(
                history_data.loc[start_index + j, 'high']
                < history_data.loc[start_index + j, f"ema{ema_period}"]
                for j in range(1, entry_k_count + 1)
            ):
                trigger_price = min(
                    history_data.loc[start_index + 1:start_index + entry_k_count + 1, 'low']
                ) - tick_size_custom

                # 挂空单
                if not has_short_position and not has_open_short and funds > 0:
                    position_size = funds * position_size_ratio * leverage / trigger_price
                    has_open_short = True
                    trades.append((row['timestamp'], 'OPEN_SHORT', trigger_price, position_size))
                    print(f"挂空头触发单: SHORT @ {trigger_price}, 仓位: {position_size}")

                # 挂平多单
                if has_long_position and not has_open_close_long:
                    close_price = trigger_price
                    has_open_close_long = True
                    trades.append((row['timestamp'], 'OPEN_CLOSE_LONG', close_price, long_position_size))
                    print(f"挂平多单: LONG @ {close_price}, 仓位: {long_position_size}")

        # 挂单触发逻辑
        if has_open_long and row['high'] >= trigger_price:
            has_open_long = False
            has_long_position = True
            long_position_size = position_size
            long_entry_price = trigger_price
            funds -= position_size * trigger_price / leverage  # 开仓扣除保证金
            trades.append((row['timestamp'], 'LONG', trigger_price, position_size))
            print(f"多头挂单成交: LONG @ {trigger_price}, 仓位: {position_size}")

        if has_open_short and row['low'] <= trigger_price:
            has_open_short = False
            has_short_position = True
            short_position_size = position_size
            short_entry_price = trigger_price
            funds -= position_size * trigger_price / leverage  # 开仓扣除保证金
            trades.append((row['timestamp'], 'SHORT', trigger_price, position_size))
            print(f"空头挂单成交: SHORT @ {trigger_price}, 仓位: {position_size}")

        # 平多单触发
        if has_open_close_long and row['low'] <= close_price:
            has_open_close_long = False
            has_long_position = False
            pnl += (close_price - long_entry_price) * long_position_size
            funds += long_position_size * close_price / leverage  # 平仓释放保证金
            trades.append((row['timestamp'], 'CLOSE_LONG', close_price, long_position_size))
            print(f"平多单成交: LONG @ {close_price}, 仓位: {long_position_size}")

        # 平空单触发
        if has_open_close_short and row['high'] >= close_price:
            has_open_close_short = False
            has_short_position = False
            pnl += (short_entry_price - close_price) * short_position_size
            funds += short_position_size * close_price / leverage  # 平仓释放保证金
            trades.append((row['timestamp'], 'CLOSE_SHORT', close_price, short_position_size))
            print(f"平空单成交: SHORT @ {close_price}, 仓位: {short_position_size}")

    return trades, pnl, funds



# # 交易逻辑
# def execute_trading(row, index):
#     global position, entry_price, pnl, balance, position_size

#     if index < ema_period:
#         return  # 跳过EMA计算不足的初始数据

#     # 交易逻辑
#     current_price = row['close']
#     current_ema = row[f"ema{ema_period}"]

#     # 检查是否满足开仓条件
#     if position is None:
#         if all(history_data.iloc[index - i]['low'] > history_data.iloc[index - i][f"ema{ema_period}"] for i in range(1, entry_k_count)):
#             trigger_price = max(history_data.iloc[index - entry_k_count:index]['high']) + tick_size_custom
#             position_size = (balance * position_size_ratio * leverage) / trigger_price
#             position = "LONG"
#             entry_price = trigger_price
#             logger.info(f"开多单: {position} @ {entry_price}, 仓位: {position_size:.4f}")
#             trades.append((row['timestamp'], position, entry_price, position_size))

#         elif all(history_data.iloc[index - i]['high'] < history_data.iloc[index - i][f"ema{ema_period}"] for i in range(1, entry_k_count)):
#             trigger_price = min(history_data.iloc[index - entry_k_count:index]['low']) - tick_size_custom
#             position_size = (balance * position_size_ratio * leverage) / trigger_price
#             position = "SHORT"
#             entry_price = trigger_price
#             logger.info(f"开空单: {position} @ {entry_price}, 仓位: {position_size:.4f}")
#             trades.append((row['timestamp'], position, entry_price, position_size))

#     # 检查是否满足平仓条件
#     elif position == "LONG" and row['low'] < current_ema:
#         pnl += (current_price - entry_price) * position_size
#         balance += pnl
#         logger.info(f"平多单: 盈亏 {pnl:.2f}, 当前余额: {balance:.2f}")
#         position = None
#         position_size = 0.0  # 平仓后重置持仓规模

#     elif position == "SHORT" and row['high'] > current_ema:
#         pnl += (entry_price - current_price) * position_size
#         balance += pnl
#         logger.info(f"平空单: 盈亏 {pnl:.2f}, 当前余额: {balance:.2f}")
#         position = None
#         position_size = 0.0  # 平仓后重置持仓规模

# 读取历史数据
# 假设历史数据以CSV格式存储，包含以下列：timestamp, open, high, low, close, volume
history_file = "historical_data.csv"  # 文件路径
history_data = pd.read_csv(history_file)

# 预处理数据
history_data['timestamp'] = pd.to_datetime(history_data['timestamp'])
# history_data[f"ema{ema_period}"] = history_data['close'].ewm(span=ema_period).mean().round(1)
# 手动指定previous_ema，因为计算ema需要至少{ema_period}根数据，不如直接指定上一根ema的值，由其去计算后一根的
history_data[f"ema{ema_period}"] = calculate_ema(history_data, ema_period, previous_ema=97939.1)
logger.debug(f"历史K线数据表如下:\n" + format_dataframe_with_tabulate(history_data.head(50), indent=32))

# 执行回测
# for index, row in history_data.iterrows():
#     execute_trading(row, index)
# 执行回测---还需修改，目前pnl和final_funds的计算还是有问题
trades, pnl, final_funds = backtest(
    history_data=history_data,
    ema_period=ema_period,
    entry_k_count=entry_k_count,
    tick_size_custom=tick_size_custom,
    leverage=leverage,
    position_size_ratio=position_size_ratio,
    initial_funds=initial_funds
)

# 输出回测结果
# logger.info(f"初始资金: {initial_balance:.2f}")
# logger.info(f"最终资金: {balance:.2f}")
# logger.info(f"总盈亏: {balance - initial_balance:.2f}")

print(f"总盈亏: {pnl}")
print(f"期末资金: {final_funds}")

# 转换为 DataFrame
columns = ["Timestamp", "Position", "Price", "Size"]
trades_df = pd.DataFrame(trades, columns=columns)

# 格式化输出
logger.info("交易记录：")
logger.info("\n"+tabulate(trades_df, headers="keys", tablefmt="pretty", showindex=False))
# logger.info(f"交易记录: {trades}")
