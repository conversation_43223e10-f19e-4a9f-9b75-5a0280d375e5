```
    ⚔️  ═══════════════════════════════════════ ⚔️
       ██████  ██████  ██████  ███████  █████  ██ ██████
      ██      ██    ██ ██   ██ ██      ██   ██ ██ ██   ██
      ██      ██    ██ ██████  ███████ ███████ ██ ██████
      ██      ██    ██ ██   ██      ██ ██   ██ ██ ██   ██
       ██████  ██████  ██   ██ ███████ ██   ██ ██ ██   ██

    🏴‍☠️                    QUANT                     ⚓

         ⛵ ～～～～～～～～～～～～～～～～～～～～～ ⛵
```

<div align="center">

# 🚀 CorsairQuant

**一个功能完整的加密货币量化交易系统**

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Status](https://img.shields.io/badge/Status-Active-brightgreen.svg)]()

*支持多种监控逻辑、自动交易逻辑、Telegram远程控制等功能*

</div>

## ✨ 主要特性

- 🔍 **多维度监控**: 成交量、买卖压力、技术指标、持仓量等监控逻辑
- 🤖 **智能交易**: EMA交叉、情绪分析、订单簿分析等交易逻辑
- 📱 **Telegram集成**: 远程监控、控制和通知
- 🔄 **自动重启**: 支持远程重启和故障恢复
- ⚡ **配置热重载**: 无需重启程序即可更新配置
- 📊 **资源监控**: 内存使用、API调用统计
- 🛡️ **安全可靠**: 进程锁、错误处理、优雅退出
- 🎯 **易于管理**: 完整的脚本管理工具

## 📋 目录

- [核心概念](#-核心概念)
- [快速开始](#-快速开始)
- [功能模块](#-功能模块)
- [安装配置](#-安装配置)
- [使用方法](#-使用方法)
- [配置热重载](#-配置热重载)
- [Telegram配置](#-telegram配置)
- [管理脚本](#-管理脚本)
- [故障排除](#-故障排除)
- [开发指南](#-开发指南)

## 🧠 核心概念

### 📖 名词阐述

在本量化交易系统中，我们使用以下核心概念：

- **监控**：按照预设的逻辑等待特定行情出现时发出告警通知，不进行实际交易操作
- **交易**：按照预设的逻辑等待特定行情出现时直接进行下单操作，并发出交易记录
- **任务 = 策略 = 逻辑**：这三个词在本系统中是同一概念的不同表达
  - 监控任务 = 监控策略 = 监控逻辑
  - 交易任务 = 交易策略 = 交易逻辑

### 🎯 核心理念

对于量化交易系统而言，**逻辑是关键**。无论是监控还是交易，都是基于预设的逻辑规则来执行：

- **监控逻辑**：定义何时发出告警，帮助用户识别交易机会
- **交易逻辑**：定义何时执行交易，实现自动化交易决策

两者的区别在于执行结果：监控输出告警信息，交易执行实际操作。

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd CorsairQuant
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置环境

```bash
# 复制配置模板
cp .env.example .env
# 编辑配置文件
nano .env
```

### 4. 启动程序

```bash
# 使用管理脚本启动（推荐）
./quant.sh start caffeinate

# 或直接启动
python corsair_engine.py
```

## 🔧 功能模块

### 监控模块

| 模块                       | 功能             | 说明               |
| -------------------------- | ---------------- | ------------------ |
| monitor1_volume            | 成交量监控       | 监控异常成交量变化 |
| monitor2_pressure          | 买卖压力监控     | 分析订单簿买卖压力 |
| monitor3_divergence        | 背离监控         | 检测价格与指标背离 |
| monitor4_oi                | 持仓量监控       | 监控期货持仓量变化 |
| monitor5_ema_ma_crossover  | 均线交叉监控     | EMA/MA交叉信号     |
| monitor6_volume_oi_matrix  | 成交量持仓量矩阵 | 多维度数据分析     |
| monitor7_bollinger_squeeze | 布林带挤压监控   | 波动率突破信号     |

### 交易模块

| 模块              | 功能           | 说明                 |
| ----------------- | -------------- | -------------------- |
| trader1_ema       | EMA交易策略    | 基于EMA的交易策略    |
| trader2_sentiment | 情绪交易策略   | 市场情绪分析策略     |
| trader3_orderbook | 订单簿交易策略 | 基于订单簿的交易策略 |

## ⚙️ 安装配置

### 系统要求

- Python 3.8+
- 稳定的网络连接
- 交易所API密钥
- Telegram Bot Token（可选）

### 环境配置

在项目根目录下创建 **.env** 文件：

```dotenv
# 交易所API配置（必需）
API_KEY=your_api_key_here
API_SECRET=your_api_secret_here

# Telegram配置（可选）
TELEGRAM_CHAT_ID=your_chat_id

# 多Bot配置（推荐）
TELEGRAM_BOT_TOKEN_QUANT=your_bot_token_quant      # 主程序通知
TELEGRAM_BOT_TOKEN_MONITOR=your_bot_token_monitor  # 监控通知
TELEGRAM_BOT_TOKEN_TRADER=your_bot_token_trader # 交易通知

# 单Bot配置（简化版）
# TELEGRAM_BOT_TOKEN=your_bot_token
```

### 配置文件说明

程序使用YAML配置文件，位于 `config/` 目录：

- `app_config.yaml` - 主程序配置
- `telegram_config.yaml` - Telegram机器人配置
- `monitor_config.yaml` - 监控模块配置
- `trader_config.yaml` - 交易策略配置

## 🎯 使用方法

### 管理脚本（推荐）

我们提供了完整的管理脚本，支持启动、停止、重启、状态查询等功能：

```bash
# 查看帮助
./quant.sh help

# 启动程序
./quant.sh start                    # 普通启动
./quant.sh start caffeinate         # 防休眠启动（macOS推荐）

# 查看状态
./quant.sh status                   # 查看运行状态
./quant.sh logs                     # 查看最新日志
./quant.sh tail                     # 实时查看日志

# 停止程序
./quant.sh stop                     # 优雅停止
./quant.sh stop force               # 强制停止
./quant.sh stop telegram            # 等待Telegram停止命令

# 重启程序
./quant.sh restart                  # 重启程序
./quant.sh restart caffeinate       # 使用caffeinate重启
```

### 直接运行

```bash
# 普通启动
python corsair_engine.py

# macOS防休眠启动（推荐）
caffeinate python corsair_engine.py

# Windows启动
python corsair_engine.py
```

### 停止程序

```bash
# 方法1：键盘中断
Ctrl+C

# 方法2：使用停止脚本
./quant.sh stop

# 方法3：Telegram远程停止
# 在Telegram中发送：/stop 或 停止程序
```

### 重启程序

```bash
# 方法1：使用重启脚本（推荐）
./quant.sh restart                  # 普通重启
./quant.sh restart caffeinate       # 使用caffeinate重启

# 方法2：手动重启
./quant.sh stop                     # 先停止
./quant.sh start caffeinate         # 再启动

# 方法3：Telegram远程重启
# 在Telegram中发送：/restart 或 重启程序
# 注意：远程重启会保持原有的启动方式（如caffeinate）
```

## ⚡ 配置热重载

程序支持在不重启的情况下重新加载配置文件，让你可以实时调整参数。

### 🔄 Telegram远程重载（推荐）

通过Telegram群组发送命令来重载配置：

| 命令                                  | 功能         | 示例                |
| ------------------------------------- | ------------ | ------------------- |
| `/reload`, `重载配置`             | 重载所有配置 | `/reload`         |
| `/reload_monitor`, `重载监控配置` | 重载监控配置 | `/reload_monitor` |
| `/reload_app`, `重载应用配置`     | 重载应用配置 | `/reload_app`     |

**使用流程：**

1. 修改配置文件（如 `config/monitor_config.yaml`）
2. 在Telegram群组发送 `/reload_monitor`
3. 系统自动重载并回复确认消息

### 🔧 重载脚本

使用专门的重载脚本来重载配置：

```bash
# 快捷脚本（推荐）
./reload.sh                    # 重载所有配置
./reload.sh monitor            # 重载监控配置
./reload.sh trader             # 重载交易配置
./reload.sh -s                 # 显示当前配置
./reload.sh monitor -s         # 显示监控配置并重载

# Python脚本
python scripts/reload_config.py -t monitor    # 重载监控配置
python scripts/reload_config.py -t trader     # 重载交易配置
python scripts/reload_config.py -s            # 显示当前配置
python scripts/reload_config.py --help        # 查看帮助
```

### 📋 支持重载的配置

| 配置文件                        | 类型标识     | 说明         |
| ------------------------------- | ------------ | ------------ |
| `config/app_config.yaml`      | `app`      | 应用主配置   |
| `config/monitor_config.yaml`  | `monitor`  | 监控模块配置 |
| `config/exchange_config.yaml` | `exchange` | 交易所配置   |
| `config/telegram_config.yaml` | `telegram` | Telegram配置 |
| `config/trader_config.yaml`   | `trader`   | 交易策略配置 |

### 🧪 测试重载功能

```bash
# 运行测试脚本
python tests/test_config_reload.py

# 查看详细使用指南
cat docs/config_reload_guide.md
```

**注意事项：**

- ✅ 大部分参数修改后立即生效
- ⚠️ 某些配置可能需要重启任务才能完全生效
- 🔒 配置语法错误不会影响程序运行

## 📱 Telegram配置

### 远程控制命令

程序支持通过Telegram进行远程控制：

| 命令                                  | 功能         | 示例                |
| ------------------------------------- | ------------ | ------------------- |
| `/start`, `启动程序`              | 启动程序     | `/start`          |
| `/stop`, `停止程序`               | 停止程序     | `/stop`           |
| `/restart`, `重启程序`            | 重启程序     | `/restart`        |
| `/status`, `状态`                 | 查询状态     | `/status`         |
| `/reload`, `重载配置`             | 重载所有配置 | `/reload`         |
| `/reload_monitor`, `重载监控配置` | 重载监控配置 | `/reload_monitor` |
| `/reload_trader`, `重载交易配置`  | 重载交易配置 | `/reload_trader`  |

### 获取API密钥

#### 1. 交易所API配置

1. 登录交易所官网（如Binance）
2. 进入 **API管理** 页面
3. 创建新的API密钥
4. 设置适当的权限（建议只开启读取和交易权限）
5. 配置IP白名单（推荐）

**安全提示**：

- ⚠️ API密钥是敏感信息，请妥善保管
- 🔒 建议设置IP白名单限制访问
- 🛡️ 定期更换API密钥

#### 2. Telegram Bot配置

1. **创建Bot**：

   - 在Telegram中搜索 `@BotFather`
   - 发送 `/newbot` 命令
   - 按提示设置Bot名称和用户名
   - 获取Bot Token
2. **获取Chat ID**：

   - 创建Telegram群组
   - 将Bot添加到群组
   - 向群组发送消息
   - 访问：`https://api.telegram.org/bot<BOT_TOKEN>/getUpdates`
   - 在返回的JSON中找到 `chat.id`
3. **多Bot配置（推荐）**：

   - 创建3个Bot分别用于：主程序、监控、交易
   - 可以创建不同的群组来接收不同类型的消息
   - 便于消息分类和管理

## 🛠️ 管理脚本

项目提供了完整的管理脚本工具：

### 脚本列表

| 脚本                         | 平台        | 功能                   |
| ---------------------------- | ----------- | ---------------------- |
| `quant.sh`                 | macOS/Linux | 一站式管理工具（推荐） |
| `reload.sh`                | macOS/Linux | 配置重载工具           |
| `scripts/reload_config.py` | 跨平台      | 配置重载脚本           |
| `scripts/start_quant.sh`   | macOS/Linux | 启动程序               |
| `scripts/stop_quant.sh`    | macOS/Linux | 停止程序               |
| `scripts/status_quant.sh`  | macOS/Linux | 状态查询               |
| `scripts/start_quant.bat`  | Windows     | 启动程序               |
| `scripts/stop_quant.bat`   | Windows     | 停止程序               |

### 使用示例

```bash
# 一站式管理（推荐）
./quant.sh start caffeinate    # 启动
./quant.sh status              # 状态
./quant.sh logs                # 日志
./quant.sh restart             # 重启
./quant.sh stop                # 停止

# 配置重载
./reload.sh                    # 重载所有配置
./reload.sh monitor            # 重载监控配置
./reload.sh -s                 # 显示当前配置

# 单独使用
./scripts/start_quant.sh caffeinate    # 启动
./scripts/status_quant.sh              # 状态
./scripts/stop_quant.sh                # 停止
```

详细使用说明请参考：[docs/SCRIPTS_README.md](docs/SCRIPTS_README.md)

## 🔧 故障排除

### 常见问题

#### 1. 程序无法启动

```bash
# 检查Python环境
python --version

# 检查依赖
pip install -r requirements.txt

# 检查配置文件
cat .env
```

#### 2. API连接失败

- 检查API密钥是否正确
- 确认网络连接正常
- 验证IP白名单设置
- 检查API权限配置

#### 3. Telegram通知不工作

- 验证Bot Token是否正确
- 确认Chat ID是否正确
- 检查Bot是否已添加到群组
- 确认网络可以访问Telegram

#### 4. 程序意外停止

```bash
# 查看日志
./quant.sh logs

# 查看系统资源
./quant.sh status

# 强制停止后重启
./quant.sh stop force
./quant.sh start caffeinate
```

### 日志分析

日志文件位于 `logs/` 目录：

```bash
# 查看主程序日志
tail -f logs/main.log

# 查看特定监控日志
tail -f logs/monitor1_volume.log

# 查看所有日志
tail -f logs/*.log
```

### 性能优化

1. **内存优化**：

   - 定期重启程序
   - 监控内存使用情况
   - 调整监控频率
2. **网络优化**：

   - 使用稳定的网络连接
   - 配置合适的超时时间
   - 启用连接池
3. **API优化**：

   - 合理设置请求频率
   - 使用WebSocket连接
   - 启用请求缓存

## 🏗️ 系统架构

### 架构图

```mermaid
graph TB
    A[主程序 main.py] --> B[配置管理器]
    A --> C[信号处理器]
    A --> D[Telegram机器人]
    A --> E[资源监控器]

    B --> F[app_config.yaml]
    B --> G[telegram_config.yaml]
    B --> H[monitor_config.yaml]
    B --> I[trader_config.yaml]

    A --> J[Exchange管理器]
    J --> K[全局Exchange实例]
    K --> L[Binance API]

    A --> M[监控模块]
    M --> M1[成交量监控]
    M --> M2[买卖压力监控]
    M --> M3[背离监控]
    M --> M4[持仓量监控]
    M --> M5[均线交叉监控]
    M --> M6[成交量持仓量矩阵]
    M --> M7[布林带挤压监控]

    A --> N[交易模块]
    N --> N1[EMA交易]
    N --> N2[情绪交易]
    N --> N3[订单簿交易]

    M1 --> O[Exchange包装器]
    M2 --> O
    M3 --> O
    M4 --> O
    M5 --> O
    M6 --> O
    M7 --> O

    N1 --> O
    N2 --> O
    N3 --> O

    O --> K

    M --> P[Telegram通知]
    N --> P
    P --> Q[Monitor Bot]
    P --> R[Strategy Bot]

    A --> S[日志系统]
    S --> T[logs/main.log]
    S --> U[logs/monitor*.log]
    S --> V[logs/trader*.log]

    D --> W[远程控制]
    W --> X[启动/停止/重启]
    W --> Y[状态查询]

    Z[管理脚本] --> A
    Z --> Z1[quant.sh]
    Z --> Z2[scripts/start_quant.sh]
    Z --> Z3[scripts/stop_quant.sh]
    Z --> Z4[scripts/status_quant.sh]

    style A fill:#e1f5fe
    style J fill:#f3e5f5
    style M fill:#e8f5e8
    style N fill:#fff3e0
    style D fill:#fce4ec
    style S fill:#f1f8e9
```

### 核心组件说明

| 组件                     | 功能               | 说明                             |
| ------------------------ | ------------------ | -------------------------------- |
| **主程序**         | 程序入口和任务调度 | 负责启动各个模块，管理生命周期   |
| **Exchange管理器** | API连接管理        | 统一管理交易所连接，避免重复连接 |
| **监控模块**       | 市场数据监控       | 实时监控各种市场指标和技术信号   |
| **交易模块**       | 交易执行           | 基于监控数据执行具体的交易逻辑   |
| **Telegram机器人** | 远程控制和通知     | 提供远程控制和消息推送功能       |
| **配置管理器**     | 配置文件管理       | 统一管理各模块的配置参数         |
| **日志系统**       | 日志记录和管理     | 记录程序运行状态和错误信息       |
| **管理脚本**       | 程序生命周期管理   | 提供启动、停止、重启等管理功能   |

## 📚 开发指南

### 项目结构

```
CorsairQuant/
├── corsair_engine.py       # 主程序入口
├── quant.sh                # 主要管理脚本
├── reload.sh               # 配置重载脚本
├── config/                 # 配置文件目录
│   ├── app_config.yaml
│   ├── telegram_config.yaml
│   ├── monitor_config.yaml
│   └── trader_config.yaml
├── monitor/                # 监控模块
│   ├── monitor1_volume.py
│   ├── monitor2_pressure.py
│   └── ...
├── trader/                 # 交易模块
│   ├── trader1_ema.py
│   ├── trader2_sentiment.py
│   └── ...
├── utils/                  # 工具模块
│   ├── utils_logger.py
│   ├── utils_telegram.py
│   ├── exchange_manager.py
│   └── ...
├── scripts/                # 管理脚本目录
│   ├── reload_config.py    # 配置重载脚本
│   ├── start_quant.sh
│   ├── stop_quant.sh
│   ├── status_quant.sh
│   ├── start_quant.bat
│   └── stop_quant.bat
├── tests/                  # 测试文件目录
│   ├── test_config_reload.py  # 配置重载测试
│   ├── test_restart.py
│   └── ...
├── docs/                   # 文档目录
│   ├── SCRIPTS_README.md
│   └── examples/           # 示例代码
│       └── alternative_stop_methods.py
├── tools/                  # 工具脚本目录
│   ├── cleanup_and_restart.py
│   └── analyze_initial_memory.py
├── bin/                    # 可执行文件目录
│   └── quant -> ../quant.sh
├── logs/                   # 日志目录
└── requirements.txt        # 依赖列表
```

### 添加新监控

1. 在 `monitor/` 目录创建新文件
2. 继承基础监控类
3. 实现监控逻辑
4. 在配置文件中添加配置
5. 测试和部署

### 添加新交易

1. 在 `trader/` 目录创建新文件
2. 继承基础交易类
3. 实现交易逻辑
4. 在配置文件中添加配置
5. 回测和部署

### 代码规范

- 使用Python 3.8+语法
- 遵循PEP 8代码规范
- 添加适当的注释和文档
- 编写单元测试
- 使用类型提示

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交Issue和Pull Request！

1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## ⚠️ 免责声明

本软件仅供学习和研究使用。使用本软件进行实际交易的风险由用户自行承担。作者不对任何投资损失负责。

**风险提示**：

- 加密货币交易具有高风险
- 量化交易不保证盈利
- 请谨慎使用实盘交易
- 建议先在测试环境验证

## 📞 支持

如有问题，请通过以下方式联系：

- 提交 [Issue](../../issues)
- 发送邮件到：[<EMAIL>]
- Telegram群组：[群组链接]

---

⭐ 如果这个项目对您有帮助，请给个Star支持一下！
