"""监控：监控大额成交量，发出提醒"""
# 只提醒，不自动操作。收到提醒后开始重点观察此币种，等待机会手动操作。

import sys
import asyncio
import pandas as pd
import traceback
import time
from dotenv import load_dotenv
import os
from ccxt.base.errors import ExchangeNotAvailable,RequestTimeout,NetworkError

# 添加项目根目录到 sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.utils_logger import setup_logger
from utils.errors import CustomError
from utils.utils_telegram import send_telegram_message
from utils.utils_exchange_error import handle_exchange_error
from utils.constants import TASK_DIVIDER, EXCHANGE_DOWN_WAITING
from utils.utils_common import format_volume, parse_interval, get_sleep_time, format_time, format_dataframe_with_tabulate, print_ctrl_c_newline,get_closed_candle_index
from utils.exchange_wrapper import create_exchange_wrapper
from utils.config_manager import get_monitor_config

# 初始化日志记录器
current_file_name = os.path.splitext(os.path.basename(__file__))[0]
log_name = f"{current_file_name}_logger"
log_file_name = f"{current_file_name}.log"
logger = setup_logger(log_name=log_name,log_file=log_file_name)

# 加载.env文件, 读取bot_token和chat_id
load_dotenv()
TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN_MONITOR")
TELEGRAM_CHAT_ID = os.getenv("TELEGRAM_CHAT_ID")

# 获取全局exchange实例（自动预加载markets和监控API调用）
exchange = create_exchange_wrapper(current_file_name, logger)


#  ******** 👇下面的数据都不要修改👇 ********

# 定义全局变量: 交易所是否宕机状态
exchange_is_down = False

# 定义常量: 
CHN_NAME = f"成交量大量"  # 中文名称

#  ******** 👆上面的数据都不要修改👆 ********

async def check_volume_and_notify(exchange, symbol, interval, volume_threshold):
    global exchange_is_down
    try:
        formatted_threshold = format_volume(volume_threshold)  # 格式化阈值
    except Exception as e:
        logger.error(f"格式化阈值时出错: {e}, 使用原始值")
        formatted_threshold = str(volume_threshold)
    while True:
        sub_msg = f"检查 {symbol} 的 {interval} {CHN_NAME}"
        if not exchange_is_down:
            logger.info(f"定时{sub_msg}")
        else:
            msg = f"重试!\n{sub_msg}..."
            logger.info(msg.replace("\n"," "))
            # send_email(f"{current_file_name} 重试", msg)
            # 发送Telegram消息（现在有限流保护）
            try:
                await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, msg)
            except Exception as e:
                logger.error(f"发送Telegram消息失败: {e}")
        try:
            # 获取最近两根K线数据
            klines = await exchange.fetch_ohlcv(symbol, timeframe=interval, limit=2)
            klines = pd.DataFrame(klines, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            klines['timestamp'] = pd.to_datetime(klines['timestamp'], unit='ms') + pd.Timedelta(hours=8)  # +8小时, 就是北京时间了
            logger.debug(f"历史K线数据表如下:\n" + format_dataframe_with_tabulate(klines.tail(20), indent=32))
            if exchange_is_down:
                exchange_is_down = False
                # 交易所服务正常了
                e_msg = f"✅ 交易所服务正常了"
                logger.info(f"{current_file_name} {e_msg}")
                # send_email(f"{current_file_name} ✅ 交易所服务正常了", {e_msg})
                # 发送Telegram消息（现在有限流保护）
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, e_msg)
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")
            # logger.debug(klines)
            # latest_kline = klines.iloc[-1]  # 最新一根的K线数据
            # 动态获取已收盘 K 线索引
            try:
                closed_candle_idx = get_closed_candle_index(klines, interval)
                logger.debug(f"索引为：{closed_candle_idx}")
                last_kline = klines.iloc[closed_candle_idx]  # 最新一根已收盘的k线

                # 解析K线数据
                timestamp = last_kline.iloc[0]  # 时间
                # volume = last_kline.iloc[5] + latest_kline.iloc[5]     # 为了更准确，把两根K线的成交量相加
                volume = last_kline.iloc[5]    # "把两根K线的成交量相加"会导致发送的信息太频繁，还是只取前一根K线的成交量好一点吧
                # logger.debug(f"时间: {timestamp}, 成交量: {volume}")
                formatted_volume = format_volume(volume)  # 格式化成交量
            except Exception as e:
                logger.error(f"处理K线数据时出错: {e}")
                continue  # 跳过本次循环，继续下一次检查

            # 检查成交量是否超过阈值
            if volume > volume_threshold:
                msg = (f"⚠️ {CHN_NAME}\n"
                       f"币种: {symbol}\n"
                       f"时间: {timestamp}\n"
                       f"周期: {interval}\n"
                       f"成交量: {formatted_volume}, 超过了 {formatted_threshold}")
                logger.info(msg.replace("\n",", "))
                # 发送消息
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"\n{msg}")
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

            else:
                logger.debug(f"{symbol}, {timestamp}, 周期 {interval}, 成交量 {formatted_volume}, 未超过")
                
        except (ExchangeNotAvailable, RequestTimeout, NetworkError) as e:
            logger.info(f"异常: {e}")
            exchange_is_down = True
            # 交易所服务宕机或不可用, 一会继续重试
            e_msg = f"❎ 交易所服务异常 ({type(e).__name__}), 暂无法 {sub_msg}, {EXCHANGE_DOWN_WAITING} 后重试..."
            logger.info(f"{current_file_name} {e_msg}")
            # send_email(f"{current_file_name} ❎ 交易所服务宕机", {e_msg})
            # 使用防重复发送工具
            await handle_exchange_error(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, e_msg)

            # 随机延迟重试，好像意义不大，因为每个任务复杂度不一样、执行时间也不一样，几乎不会出现同时重试
            wait_time = parse_interval(EXCHANGE_DOWN_WAITING)
            await asyncio.sleep(wait_time)
        except Exception as e:
            logger.error(f"检查过程中出错了:")
            raise e

        if not exchange_is_down:
            # 等待一段时间后再次检查
            # wait_time = 60
            # wait_time = random.randint(60, 90)  # 随机生成60到90秒之间的等待时间
            # wait_time = parse_interval(interval)  # sleep固定间隔
            # ftime = interval
            try:
                current_timestamp = await exchange.fetch_time() # 获取服务器时间（毫秒时间戳）
            except (ExchangeNotAvailable, RequestTimeout, NetworkError) as e:
                logger.warning(f"获取服务器时间失败: {e}, 使用本地时间")
                # 如果获取服务器时间失败，使用本地时间（毫秒时间戳）
                current_timestamp = int(time.time() * 1000)
            except Exception as e:
                logger.error(f"获取服务器时间时发生未知错误: {e}, 使用本地时间")
                current_timestamp = int(time.time() * 1000)
            
            # 根据 interval 动态调整 sleep 时间
            try:
                wait_time = get_sleep_time(current_timestamp, interval)
                ftime = format_time(wait_time)
            except Exception as e:
                logger.error(f"计算等待时间时出错: {e}, 使用默认间隔")
                wait_time = parse_interval(interval)
                ftime = format_time(wait_time)
            logger.info(f"等待 {ftime} 后再次{sub_msg}...")
            await asyncio.sleep(wait_time)  # 每隔一段时间检查一次

            # time.sleep(sleep_time)  # 等待所需的时间

async def main():

    logger.info(f"{current_file_name} 开始！")
    # send_email(f"{current_file_name} 开始", "启动啦")
    # 发送Telegram消息
    try:
        await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"开始！")
    except Exception as e:
        logger.error(f"发送Telegram消息失败: {e}")

    try:

        # 只检查一组数据
        # await check_volume_and_notify(exchange, trade_symbol, timeframe, volume_threshold)

        # 从配置文件获取监控配置
        symbols = get_monitor_config('monitor.common.symbols', ["BTC/USDT:USDT"])
        intervals = get_monitor_config('monitor.common.intervals', ["5m"])
        threshold_multiplier = get_monitor_config('monitor.volume.threshold_multiplier', 2.0)

        # 构建配置列表 - 这里使用简单的基础阈值，实际应该根据历史数据计算
        base_volume_thresholds = {
            "BTC/USDT:USDT": 1_000,
            "ETH/USDT:USDT": 500_000,
        }

        configs = []
        for symbol in symbols:
            for interval in intervals:
                base_threshold = base_volume_thresholds.get(symbol, 1_000)
                volume_threshold = base_threshold * threshold_multiplier
                configs.append({
                    "symbol": symbol,
                    "interval": interval,
                    "volume_threshold": volume_threshold
                })

        logger.info(f"📋 从配置加载: {len(symbols)}个币种, {len(intervals)}个时间间隔, 阈值倍数{threshold_multiplier}")

        # 创建任务列表
        tasks = []
        for i,config in enumerate(configs):
            task = asyncio.create_task(check_volume_and_notify(exchange, config["symbol"], config["interval"], config["volume_threshold"]),name=f"{current_file_name}_{i}")
            tasks.append(task)
            await asyncio.sleep(0.5)  # 延迟 0.5 秒后启动下一个任务

        # 等待所有任务完成
        await asyncio.gather(*tasks)
        

    except CustomError as e:
        reason = f"{str(e)}"
    except asyncio.CancelledError:
        # 任务被取消，输出换行符避免与^C共行
        print_ctrl_c_newline()
        reason = f"主动取消"
    except Exception as e:
        logger.error(f"报错: {traceback.format_exc()}")
        reason = f"报错: {repr(e)}"
    finally:
        # 显示 `reason` 的默认值，以防止未设置导致的问题
        if 'reason' not in locals():  # 判断 reason 是否被赋值
            reason = "执行结束"
        # send_email(f"{current_file_name} 停止", f"{reason}")
        # 发送Telegram消息
        try:
            await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"停止！原因: {reason}")
        except Exception as e:
            logger.error(f"发送Telegram消息失败: {e}")
        # exchange由全局管理，无需在此处关闭
        logger.info(f"{current_file_name} 停止！原因: {reason}")
        return {"task_name":current_file_name, "task_result":reason}

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        # 任务被取消，输出换行符避免与^C共行
        print_ctrl_c_newline()
        logger.info(f"主动取消")
    finally:
        # 程序级清理（关闭exchange）- 只在独立运行时执行
        from utils.exchange_manager import close_global_exchange
        try:
            asyncio.run(close_global_exchange())
            logger.info("✅ Exchange连接已关闭")
        except Exception as e:
            logger.error(f"Exchange关闭时出错: {e}")