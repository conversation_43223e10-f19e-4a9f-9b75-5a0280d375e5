"""监控：监控币种未平仓合约数（OI）变动，发出提醒"""
# 定期检查币种的未平仓合约数，如果变动超过阈值，则发消息到telegram

import sys
import asyncio
import traceback
import time
from dotenv import load_dotenv
import os
from ccxt.base.errors import ExchangeNotAvailable, RequestTimeout, NetworkError

# 添加项目根目录到 sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.utils_logger import setup_logger
from errors import CustomError
from utils.utils_telegram import send_telegram_message
from utils.utils_exchange_error import handle_exchange_error
from constants import TASK_DIVIDER, EXCHANGE_DOWN_WAITING
from utils.utils_common import parse_interval, format_time, get_sleep_time, print_ctrl_c_newline
from utils.exchange_wrapper import create_exchange_wrapper
from utils.config_manager import get_monitor_config

# 初始化日志记录器
current_file_name = os.path.splitext(os.path.basename(__file__))[0]
log_name = f"{current_file_name}_logger"
log_file_name = f"{current_file_name}.log"
logger = setup_logger(log_name=log_name,log_file=log_file_name)

# 加载.env文件, 读取bot_token和chat_id
load_dotenv()
TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN_MONITOR")
TELEGRAM_CHAT_ID = os.getenv("TELEGRAM_CHAT_ID")

# 使用全局exchange包装器
exchange = create_exchange_wrapper(current_file_name, logger)

#  ******** 👇下面的数据都不要修改👇 ********

# 定义全局变量: 交易所是否宕机状态
exchange_is_down = False

# 定义常量:
CHN_NAME = f"合约持仓"  # 中文名称

# 存储上次的未平仓合约数数据
last_open_interests = {}

#  ******** 👆上面的数据都不要修改👆 ********

def format_open_interest(size):
    """格式化未平仓合约数显示"""
    try:
        if size >= 1_000_000:
            return f"{size / 1_000_000:.2f}M"
        elif size >= 1_000:
            return f"{size / 1_000:.2f}K"
        else:
            return f"{size:.4f}"
    except:
        return str(size)

async def check_open_interest_and_notify(exchange, symbol, interval, change_threshold):
    global exchange_is_down, last_open_interests

    while True:
        sub_msg = f"检查 {symbol} 的 {interval} {CHN_NAME}变动"
        if not exchange_is_down:
            logger.info(f"定时{sub_msg}")
        else:
            msg = f"重试!\n{sub_msg}..."
            logger.info(msg.replace("\n"," "))
            # send_email(f"{current_file_name} 重试", msg)
            # 发送Telegram消息（现在有限流保护）
            try:
                await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, msg)
            except Exception as e:
                logger.error(f"发送Telegram消息失败: {e}")

        try:
            # 获取当前未平仓合约数
            oi_data = await exchange.fetch_open_interest(symbol)
            current_open_interest = float(oi_data['openInterestAmount'])

            logger.info(f"当前 {symbol} 未平仓合约数: {format_open_interest(current_open_interest)}")
            
            if exchange_is_down:
                exchange_is_down = False
                # 交易所服务正常了
                e_msg = f"✅ 交易所服务正常了"
                logger.info(f"{current_file_name} {e_msg}")
                # send_email(f"{current_file_name} ✅ 交易所服务正常了", {e_msg})
                # 发送Telegram消息（现在有限流保护）
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, e_msg)
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

            # 检查是否有历史数据进行比较
            if symbol in last_open_interests:
                last_oi = last_open_interests[symbol]

                # 计算变动百分比
                if last_oi > 0:
                    change_percent = abs(current_open_interest - last_oi) / last_oi * 100

                    logger.debug(f"上次{CHN_NAME}: {format_open_interest(last_oi)}, "
                               f"当前{CHN_NAME}: {format_open_interest(current_open_interest)}, "
                               f"变动: {change_percent:.2f}%")

                    # 检查变动是否超过阈值
                    if change_percent > change_threshold:
                        change_direction = "增加" if current_open_interest > last_oi else "减少"
                        msg = (f"⚠️ {CHN_NAME}{change_direction}\n"
                              f"币种: {symbol}\n"
                              f"过去 {interval} {change_direction} {change_percent:.2f}%\n"
                              f"当前{CHN_NAME}: {format_open_interest(current_open_interest)}\n"
                              f"上次{CHN_NAME}: {format_open_interest(last_oi)}")

                        logger.info(msg.replace("\n",", "))
                        # 发送消息
                        try:
                            await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"\n{msg}")
                        except Exception as e:
                            logger.error(f"发送Telegram消息失败: {e}")

                    else:
                        logger.debug(f"{symbol} , 周期 {interval} , 未平仓合约数变动 {change_percent:.2f}%，未超过阈值 {change_threshold}%")
                else:
                    # 从0到有数据（理论上不太可能发生）
                    logger.info(f"{symbol} 未平仓合约数从0变为: {format_open_interest(current_open_interest)}")
            else:
                # 首次检查，记录初始未平仓合约数
                logger.info(f"首次检查 {symbol}，当前{CHN_NAME}: {format_open_interest(current_open_interest)}")

            # 更新历史未平仓合约数
            last_open_interests[symbol] = current_open_interest
                
        except (ExchangeNotAvailable, RequestTimeout, NetworkError) as e:
            logger.info(f"异常: {e}")
            exchange_is_down = True
            # 交易所服务宕机或不可用, 一会继续重试
            e_msg = f"❎ 交易所服务异常 ({type(e).__name__}), 暂无法 {sub_msg}, {EXCHANGE_DOWN_WAITING} 后重试..."
            logger.info(f"{current_file_name} {e_msg}")
            # send_email(f"{current_file_name} ❎ 交易所服务宕机", {e_msg})
            # 使用防重复发送工具
            await handle_exchange_error(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, e_msg)

            # 随机延迟重试，好像意义不大，因为每个任务复杂度不一样、执行时间也不一样，几乎不会出现同时重试
            wait_time = parse_interval(EXCHANGE_DOWN_WAITING)
            await asyncio.sleep(wait_time)
        except Exception as e:
            logger.error(f"检查过程中出错了: {e}")
            raise e

        if not exchange_is_down:
            # 等待到下一个K线收盘后再次检查
            try:
                current_timestamp = await exchange.fetch_time() # 获取服务器时间（毫秒时间戳）
            except (ExchangeNotAvailable, RequestTimeout, NetworkError) as e:
                logger.warning(f"获取服务器时间失败: {e}, 使用本地时间")
                # 如果获取服务器时间失败，使用本地时间（毫秒时间戳）
                current_timestamp = int(time.time() * 1000)
            except Exception as e:
                logger.error(f"获取服务器时间时发生未知错误: {e}, 使用本地时间")
                current_timestamp = int(time.time() * 1000)

            # 根据 interval 动态调整 sleep 时间
            try:
                wait_time = get_sleep_time(current_timestamp, interval)
                ftime = format_time(wait_time)
            except Exception as e:
                logger.error(f"计算等待时间时出错: {e}, 使用默认间隔")
                wait_time = parse_interval(interval)
                ftime = format_time(wait_time)
            logger.info(f"等待 {ftime} 后再次{sub_msg}...")
            await asyncio.sleep(wait_time)

async def main():

    logger.info(f"{current_file_name} 开始！")
    # send_email(f"{current_file_name} 开始", "启动啦")
    # 发送Telegram消息
    try:
        await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"开始！")
    except Exception as e:
        logger.error(f"发送Telegram消息失败: {e}")

    try:

        # 只检查一组数据
        # await check_open_interest_and_notify(exchange, trade_symbol, timeframe, change_threshold)

        # 从配置文件获取监控配置
        symbols = get_monitor_config('monitor.common.symbols', ["BTC/USDT:USDT"])
        intervals = get_monitor_config('monitor.common.intervals', ["15m"])
        threshold_percentage = get_monitor_config('monitor.open_interest.threshold_percentage', 5.0)

        # 构建配置列表
        configs = []
        for symbol in symbols:
            for interval in intervals:
                configs.append({
                    "symbol": symbol,
                    "interval": interval,
                    "change_threshold": threshold_percentage
                })

        logger.info(f"📋 从配置加载: {len(symbols)}个币种, {len(intervals)}个时间间隔, OI变动阈值{threshold_percentage}%")

        # 创建任务列表
        tasks = []
        for i,config in enumerate(configs):
            task = asyncio.create_task(
                check_open_interest_and_notify(
                    exchange,
                    config["symbol"],
                    config["interval"],
                    config["change_threshold"]
                ),
                name=f"{current_file_name}_{i}"
            )
            tasks.append(task)
            await asyncio.sleep(0.5)  # 延迟 0.5 秒后启动下一个任务

        # 等待所有任务完成
        await asyncio.gather(*tasks)
        

    except CustomError as e:
        reason = f"{str(e)}"
    except asyncio.CancelledError:
        # 任务被取消，输出换行符避免与^C共行
        print_ctrl_c_newline()
        reason = f"主动取消"
    except Exception as e:
        logger.error(f"报错: {traceback.format_exc()}")
        reason = f"报错: {repr(e)}"
    finally:
        # 显示 `reason` 的默认值，以防止未设置导致的问题
        if 'reason' not in locals():  # 判断 reason 是否被赋值
            reason = "执行结束"
        # send_email(f"{current_file_name} 停止", f"{reason}")
        # 发送Telegram消息
        try:
            await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"停止！原因: {reason}")
        except Exception as e:
            logger.error(f"发送Telegram消息失败: {e}")
        # exchange由全局管理，无需在此处关闭
        logger.info(f"{current_file_name} 停止！原因: {reason}")
        return {"task_name":current_file_name, "task_result":reason}

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        # 任务被取消，输出换行符避免与^C共行
        print_ctrl_c_newline()
        logger.info(f"主动取消")
    finally:
        # 程序级清理（关闭exchange）- 只在独立运行时执行
        from utils.exchange_manager import close_global_exchange
        try:
            asyncio.run(close_global_exchange())
            logger.info("✅ Exchange连接已关闭")
        except Exception as e:
            logger.error(f"Exchange关闭时出错: {e}")
