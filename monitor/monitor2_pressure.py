"""监控：通过盘口数据监控买压和卖压强度"""
# 通过分析盘口数据（order book）来判断买压和卖压的强度对比
# 当买卖压力出现明显不平衡时发出提醒，帮助判断市场趋势

import sys
import asyncio
import pandas as pd
import traceback
import time
import os
from dotenv import load_dotenv
from ccxt.base.errors import ExchangeNotAvailable, RequestTimeout, NetworkError

# 添加项目根目录到 sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.utils_logger import setup_logger
from errors import CustomError
from utils.utils_telegram import send_telegram_message
from utils.utils_exchange_error import handle_exchange_error
from constants import TASK_DIVIDER, EXCHANGE_DOWN_WAITING
from utils.utils_common import parse_interval, get_sleep_time, format_time, print_ctrl_c_newline
from utils.exchange_wrapper import create_exchange_wrapper
from utils.config_manager import get_monitor_config

# 初始化日志记录器
current_file_name = os.path.splitext(os.path.basename(__file__))[0]
log_name = f"{current_file_name}_logger"
log_file_name = f"{current_file_name}.log"
logger = setup_logger(log_name=log_name,log_file=log_file_name)

# 加载.env文件, 读取bot_token和chat_id
load_dotenv()
TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN_MONITOR")
TELEGRAM_CHAT_ID = os.getenv("TELEGRAM_CHAT_ID")

# 使用全局exchange包装器
exchange = create_exchange_wrapper(current_file_name, logger)

#  ******** 👇下面的数据都不要修改👇 ********

# 定义全局变量: 交易所是否宕机状态
exchange_is_down = False

# 定义常量:
CHN_NAME = f"买/卖压"  # 中文名称

# 存储每个币种的历史数据，用于对比分析
symbol_history = {}

#  ******** 👆上面的数据都不要修改👆 ********

def format_amount(amount):
    """格式化金额显示"""
    if amount >= 1_000_000:
        return f"{amount / 1_000_000:.2f}M"
    elif amount >= 1_000:
        return f"{amount / 1_000:.2f}K"
    else:
        return f"{amount:.2f}"

def calculate_pressure_metrics(orderbook):
    """计算买压和卖压指标"""
    bids = orderbook['bids']  # 买单 [[price, amount], ...]
    asks = orderbook['asks']  # 卖单 [[price, amount], ...]

    if not bids or not asks:
        return None

    # 计算前5档的买卖压力
    depth = min(5, len(bids), len(asks))

    # 买压：前5档买单的总金额
    bid_pressure = sum(price * amount for price, amount in bids[:depth])

    # 卖压：前5档卖单的总金额
    ask_pressure = sum(price * amount for price, amount in asks[:depth])

    # 总压力
    total_pressure = bid_pressure + ask_pressure

    if total_pressure == 0:
        return None

    # 买压占比
    bid_ratio = bid_pressure / total_pressure * 100

    # 卖压占比
    ask_ratio = ask_pressure / total_pressure * 100

    # 压力比值（买压/卖压）
    pressure_ratio = bid_pressure / ask_pressure if ask_pressure > 0 else float('inf')

    return {
        'bid_pressure': bid_pressure,
        'ask_pressure': ask_pressure,
        'bid_ratio': bid_ratio,
        'ask_ratio': ask_ratio,
        'pressure_ratio': pressure_ratio,
        'total_pressure': total_pressure
    }

async def check_pressure_and_notify(exchange, symbol, interval, pressure_threshold):
    global exchange_is_down, symbol_history

    # 初始化币种历史数据
    if symbol not in symbol_history:
        symbol_history[symbol] = {
            'last_pressure_metrics': None,
            'last_alert_time': None,
            'alert_count': 0
        }
    while True:
        sub_msg = f"检查 {symbol} 的 {CHN_NAME}"
        if not exchange_is_down:
            logger.info(f"定时{sub_msg}")
        else:
            msg = f"重试!\n{sub_msg}..."
            logger.info(msg.replace("\n"," "))
            try:
                await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, msg)
            except Exception as e:
                logger.error(f"发送Telegram消息失败: {e}")

        try:
            # 获取盘口数据
            orderbook = await exchange.fetch_order_book(symbol, limit=10)
            current_time = pd.Timestamp.now()  # 当前时间

            logger.info(f"盘口数据获取成功，买单数量: {len(orderbook['bids'])}, 卖单数量: {len(orderbook['asks'])}")
            if exchange_is_down:
                exchange_is_down = False
                e_msg = f"✅ 交易所服务正常了"
                logger.info(f"{current_file_name} {e_msg}")
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, e_msg)
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

            # 计算买卖压力指标
            pressure_metrics = calculate_pressure_metrics(orderbook)

            if pressure_metrics is None:
                logger.warning(f"{symbol} 盘口数据不足，跳过本次检查")
                continue

            # 获取历史数据
            history = symbol_history[symbol]

            # 基础消息内容
            base_msg = (f"币种: {symbol}\n"
                       f"时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
                       f"买压: {format_amount(pressure_metrics['bid_pressure'])} ({pressure_metrics['bid_ratio']:.1f}%)\n"
                       f"卖压: {format_amount(pressure_metrics['ask_pressure'])} ({pressure_metrics['ask_ratio']:.1f}%)\n"
                       f"压力比值: {pressure_metrics['pressure_ratio']:.2f}")

            # 判断是否需要发送提醒
            should_alert = False
            alert_msg = ""

            # 强买压：买压占比超过阈值
            if pressure_metrics['bid_ratio'] > pressure_threshold:
                should_alert = True
                alert_msg = f"🟢 强买压信号\n{base_msg}\n买方力量明显占优，可能推动价格上涨"

            # 强卖压：卖压占比超过阈值
            elif pressure_metrics['ask_ratio'] > pressure_threshold:
                should_alert = True
                alert_msg = f"🔴 强卖压信号\n{base_msg}\n卖方力量明显占优，可能推动价格下跌"

            # 发送提醒
            if should_alert:
                logger.info(alert_msg.replace("\n", ", "))
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"\n{alert_msg}")
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

                # 更新历史记录
                history['last_alert_time'] = current_time
                history['alert_count'] += 1
            else:
                logger.debug(f"{symbol}, 买压 {pressure_metrics['bid_ratio']:.1f}%, 卖压 {pressure_metrics['ask_ratio']:.1f}%, 未触发阈值 {pressure_threshold}%")

            # 更新历史数据
            history['last_pressure_metrics'] = pressure_metrics
                
        except (ExchangeNotAvailable, RequestTimeout, NetworkError) as e:
            logger.info(f"异常: {e}")
            exchange_is_down = True
            # 交易所服务宕机或不可用, 一会继续重试
            e_msg = f"❎ 交易所服务异常 ({type(e).__name__}), 暂无法 {sub_msg}, {EXCHANGE_DOWN_WAITING} 后重试..."
            logger.info(f"{current_file_name} {e_msg}")
            # send_email(f"{current_file_name} ❎ 交易所服务宕机", {e_msg})
            # 使用防重复发送工具
            await handle_exchange_error(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, e_msg)

            # 随机延迟重试，好像意义不大，因为每个任务复杂度不一样、执行时间也不一样，几乎不会出现同时重试
            wait_time = parse_interval(EXCHANGE_DOWN_WAITING)
            await asyncio.sleep(wait_time)
        except Exception as e:
            logger.error(f"检查过程中出错了:")
            raise e

        if not exchange_is_down:
            # 等待一段时间后再次检查
            # wait_time = 60
            # wait_time = random.randint(60, 90)  # 随机生成60到90秒之间的等待时间
            # wait_time = parse_interval(interval)  # sleep固定间隔
            # ftime = interval
            try:
                current_timestamp = await exchange.fetch_time() # 获取服务器时间（毫秒时间戳）
            except (ExchangeNotAvailable, RequestTimeout, NetworkError) as e:
                logger.warning(f"获取服务器时间失败: {e}, 使用本地时间")
                # 如果获取服务器时间失败，使用本地时间（毫秒时间戳）
                current_timestamp = int(time.time() * 1000)
            except Exception as e:
                logger.error(f"获取服务器时间时发生未知错误: {e}, 使用本地时间")
                current_timestamp = int(time.time() * 1000)
            
            # 根据 interval 动态调整 sleep 时间
            try:
                wait_time = get_sleep_time(current_timestamp, interval)
                ftime = format_time(wait_time)
            except Exception as e:
                logger.error(f"计算等待时间时出错: {e}, 使用默认间隔")
                wait_time = parse_interval(interval)
                ftime = format_time(wait_time)
            logger.info(f"等待 {ftime} 后再次{sub_msg}...")
            await asyncio.sleep(wait_time)  # 每隔一段时间检查一次

            # time.sleep(sleep_time)  # 等待所需的时间

async def main():

    logger.info(f"{current_file_name} 开始！")
    # send_email(f"{current_file_name} 开始", "启动啦")
    # 发送Telegram消息
    try:
        await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"开始！")
    except Exception as e:
        logger.error(f"发送Telegram消息失败: {e}")

    try:

        # 只检查一组数据
        # await check_pressure_and_notify(exchange, trade_symbol, timeframe, volume_threshold)

        # 从配置文件获取监控配置
        symbols = get_monitor_config('monitor.common.symbols', ["BTC/USDT:USDT"])
        intervals = get_monitor_config('monitor.common.intervals', ["30s"])
        strong_buy_threshold = get_monitor_config('monitor.pressure.strong_buy_threshold', 70.0)

        # 构建配置列表
        configs = []
        for symbol in symbols:
            for interval in intervals:
                configs.append({
                    "symbol": symbol,
                    "interval": interval,
                    "pressure_threshold": strong_buy_threshold
                })

        logger.info(f"📋 从配置加载: {len(symbols)}个币种, {len(intervals)}个时间间隔, 阈值{strong_buy_threshold}%")

        # 创建任务列表
        tasks = []
        for i, config in enumerate(configs):
            task = asyncio.create_task(
                check_pressure_and_notify(
                    exchange,
                    config["symbol"],
                    config["interval"],
                    config["pressure_threshold"]
                ),
                name=f"{current_file_name}_{i}"
            )
            tasks.append(task)
            await asyncio.sleep(0.5)  # 延迟 0.5 秒后启动下一个任务

        # 等待所有任务完成
        await asyncio.gather(*tasks)
        

    except CustomError as e:
        reason = f"{str(e)}"
    except asyncio.CancelledError:
        # 任务被取消，输出换行符避免与^C共行
        print_ctrl_c_newline()
        reason = f"主动取消"
    except Exception as e:
        logger.error(f"报错: {traceback.format_exc()}")
        reason = f"报错: {repr(e)}"
    finally:
        # 显示 `reason` 的默认值，以防止未设置导致的问题
        if 'reason' not in locals():  # 判断 reason 是否被赋值
            reason = "执行结束"
        # send_email(f"{current_file_name} 停止", f"{reason}")
        # 发送Telegram消息
        try:
            await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"停止！原因: {reason}")
        except Exception as e:
            logger.error(f"发送Telegram消息失败: {e}")
        # exchange由全局管理，无需在此处关闭
        logger.info(f"{current_file_name} 停止！原因: {reason}")
        return {"task_name":current_file_name, "task_result":reason}

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        # 任务被取消，输出换行符避免与^C共行
        print_ctrl_c_newline()
        logger.info(f"主动取消")
    finally:
        # 程序级清理（关闭exchange）- 只在独立运行时执行
        from utils.exchange_manager import close_global_exchange
        try:
            asyncio.run(close_global_exchange())
            logger.info("✅ Exchange连接已关闭")
        except Exception as e:
            logger.error(f"Exchange关闭时出错: {e}")