"""监控：布林带收口形态检测"""
# 监控布林带上下轨道的收窄，当布林带宽度小于阈值时发出提醒
# 布林带收口通常预示着即将出现大幅波动

import sys
import asyncio
import pandas as pd
import traceback
import time
import os
from dotenv import load_dotenv
from ccxt.base.errors import ExchangeNotAvailable, RequestTimeout, NetworkError

# 添加项目根目录到 sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.utils_logger import setup_logger
from utils.errors import CustomError
from utils.utils_telegram import send_telegram_message
from utils.utils_exchange_error import handle_exchange_error
from utils.constants import TASK_DIVIDER, EXCHANGE_DOWN_WAITING
from utils.utils_common import parse_interval, get_sleep_time, format_time, format_dataframe_with_tabulate, get_closed_candle_index, print_ctrl_c_newline
from utils.exchange_wrapper import create_exchange_wrapper
from utils.config_manager import get_monitor_config

# 初始化日志记录器
current_file_name = os.path.splitext(os.path.basename(__file__))[0]
log_name = f"{current_file_name}_logger"
log_file_name = f"{current_file_name}.log"
logger = setup_logger(log_name=log_name, log_file=log_file_name)

# 加载.env文件, 读取bot_token和chat_id
load_dotenv()
TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN_MONITOR")
TELEGRAM_CHAT_ID = os.getenv("TELEGRAM_CHAT_ID")

# 使用全局exchange包装器
exchange = create_exchange_wrapper(current_file_name, logger)

#  ******** 👇下面的数据都不要修改👇 ********

# 定义全局变量: 交易所是否宕机状态
exchange_is_down = False

# 定义常量: 
CHN_NAME = f"布林带收口"  # 中文名称

# 存储每个币种的状态
symbol_states = {}

#  ******** 👆上面的数据都不要修改👆 ********

# 获取历史 K 线数据
async def fetch_ohlcv(exchange, symbol, interval='15m', limit=100):
    data = await exchange.fetch_ohlcv(symbol, interval, limit=limit)
    return pd.DataFrame(data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

# 计算布林带指标
def calculate_bollinger_bands(data, window=20, num_std=2):
    # 计算移动平均线
    data['sma'] = data['close'].rolling(window=window).mean()
    
    # 计算标准差
    data['std'] = data['close'].rolling(window=window).std()
    
    # 计算布林带上轨、下轨
    data['upper_band'] = data['sma'] + (data['std'] * num_std)
    data['lower_band'] = data['sma'] - (data['std'] * num_std)
    
    # 计算布林带宽度 (相对于中轨的百分比)
    data['bb_width'] = (data['upper_band'] - data['lower_band']) / data['sma'] * 100
    
    # 计算布林带宽度的移动平均
    data['bb_width_ma'] = data['bb_width'].rolling(window=10).mean()
    
    return data

# 检测布林带收口
def detect_bollinger_squeeze(data, threshold):
    # 当布林带宽度小于阈值时，标记为收口
    data['squeeze'] = (data['bb_width'] < threshold).astype(int)
    
    # 检测新的收口形成（当前K线是收口，前一K线不是）
    data['new_squeeze'] = ((data['squeeze'] == 1) & (data['squeeze'].shift(1) == 0)).astype(int)
    
    return data

async def check_bollinger_squeeze_and_notify(exchange, symbol, interval, threshold):
    global exchange_is_down, symbol_states

    # 使用symbol+interval作为状态键，避免同一symbol不同interval的状态冲突
    state_key = f"{symbol}_{interval}"

    # 初始化币种状态
    if state_key not in symbol_states:
        symbol_states[state_key] = {
            'last_squeeze_time': None,
            'in_squeeze': False
        }
    
    while True:
        sub_msg = f"检查 {symbol} 的 {interval} {CHN_NAME}"
        if not exchange_is_down:
            logger.info(f"定时{sub_msg}")
        else:
            msg = f"重试!\n{sub_msg}..."
            logger.info(msg.replace("\n", " "))
            try:
                await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, msg)
            except Exception as e:
                logger.error(f"发送Telegram消息失败: {e}")
        
        try:
            # 获取K线数据
            ohlcv = await fetch_ohlcv(exchange, symbol, interval)
            ohlcv['close'] = ohlcv['close'].astype(float)
            
            # 计算布林带指标
            ohlcv = calculate_bollinger_bands(ohlcv)
            
            # 检测布林带收口
            ohlcv = detect_bollinger_squeeze(ohlcv, threshold)
            
            # 转换时间戳为北京时间
            ohlcv['timestamp'] = pd.to_datetime(ohlcv['timestamp'], unit='ms') + pd.Timedelta(hours=8)
            
            # 打印数据表
            logger.debug(f"历史K线数据表如下:\n" + format_dataframe_with_tabulate(ohlcv.tail(20), indent=32))
            
            if exchange_is_down:
                exchange_is_down = False
                e_msg = f"✅ 交易所服务正常了"
                logger.info(f"{current_file_name} {e_msg}")
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, e_msg)
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")
            
            # 获取已收盘K线索引
            try:
                closed_candle_idx = get_closed_candle_index(ohlcv, interval)
                logger.debug(f"索引为：{closed_candle_idx}")
                last_row = ohlcv.iloc[closed_candle_idx]
            except Exception as e:
                logger.error(f"处理K线数据时出错: {e}")
                continue
            
            # 基础消息内容 - 格式化交易对符号，将基础货币部分加粗
            # symbol_parts = symbol.split('/')
            # formatted_symbol = f"*{symbol_parts[0]}*/{symbol_parts[1]}" if len(symbol_parts) >= 2 else symbol
            base_msg = (f"币种: {symbol}\n"
                       f"时间: {last_row['timestamp']}\n"
                       f"周期: {interval}\n"
                       f"收盘价: {last_row['close']:.2f}\n"
                       f"布林带宽度: {last_row['bb_width']:.2f}%")
            
            state = symbol_states[state_key]
            
            # 检测新的收口信号
            if last_row['new_squeeze'] and not state['in_squeeze']:
                state['in_squeeze'] = True
                state['last_squeeze_time'] = last_row['timestamp']
                
                msg = f"🔍 布林带收口信号\n{base_msg}\n布林带宽度低于阈值 {threshold}%"
                logger.info(msg.replace("\n", ", "))
                
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"\n{msg}")
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")
            
            # 检测收口结束
            elif not last_row['squeeze'] and state['in_squeeze']:
                state['in_squeeze'] = False
                
                # 计算收口持续时间
                if state['last_squeeze_time']:
                    duration = last_row['timestamp'] - state['last_squeeze_time']
                    duration_str = f"{duration.total_seconds() / 60:.0f}分钟"
                else:
                    duration_str = "未知"
                
                msg = f"💥 布林带收口结束，可能出现大幅波动\n{base_msg}\n收口持续时间: {duration_str}"
                logger.info(msg.replace("\n", ", "))
                
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"\n{msg}")
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

            # 检测未触发条件的情况
            if not (last_row['new_squeeze'] and not state['in_squeeze']) and not (not last_row['squeeze'] and state['in_squeeze']):
                logger.debug(f"{symbol}, {last_row['timestamp']}, 周期 {interval}, 布林带宽度 {last_row['bb_width']:.2f}%, 未触发收口信号")

        except (ExchangeNotAvailable, RequestTimeout, NetworkError) as e:
            logger.info(f"异常: {e}")
            exchange_is_down = True
            e_msg = f"❎ 交易所服务异常 ({type(e).__name__}), 暂无法 {sub_msg}, {EXCHANGE_DOWN_WAITING} 后重试..."
            logger.info(f"{current_file_name} {e_msg}")

            await handle_exchange_error(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, e_msg)

            wait_time = parse_interval(EXCHANGE_DOWN_WAITING)
            await asyncio.sleep(wait_time)
        except Exception as e:
            logger.error(f"检查过程中出错了:")
            raise e

        if not exchange_is_down:
            # 等待一段时间后再次检查
            try:
                current_timestamp = await exchange.fetch_time() # 获取服务器时间（毫秒时间戳）
            except (ExchangeNotAvailable, RequestTimeout, NetworkError) as e:
                logger.warning(f"获取服务器时间失败: {e}, 使用本地时间")
                # 如果获取服务器时间失败，使用本地时间（毫秒时间戳）
                current_timestamp = int(time.time() * 1000)
            except Exception as e:
                logger.error(f"获取服务器时间时发生未知错误: {e}, 使用本地时间")
                current_timestamp = int(time.time() * 1000)

            # 根据 interval 动态调整 sleep 时间
            try:
                wait_time = get_sleep_time(current_timestamp, interval)
                ftime = format_time(wait_time)
            except Exception as e:
                logger.error(f"计算等待时间时出错: {e}, 使用默认间隔")
                wait_time = parse_interval(interval)
                ftime = format_time(wait_time)
            logger.info(f"等待 {ftime} 后再次{sub_msg}...")
            await asyncio.sleep(wait_time)

async def main():

    logger.info(f"{current_file_name} 开始！")
    # 发送Telegram消息
    try:
        await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"开始！")
    except Exception as e:
        logger.error(f"发送Telegram消息失败: {e}")

    try:

        # 从配置文件获取监控配置
        symbols = get_monitor_config('monitor.common.symbols', ["BTC/USDT:USDT"])
        intervals = get_monitor_config('monitor.common.intervals', ["5m", "15m", "1h"])
        bb_period = get_monitor_config('monitor.bollinger_squeeze.bb_period', 20)
        bb_std = get_monitor_config('monitor.bollinger_squeeze.bb_std', 2)
        squeeze_threshold = get_monitor_config('monitor.bollinger_squeeze.squeeze_threshold', 2.0)

        # 构建配置列表
        configs = []
        for symbol in symbols:
            for interval in intervals:
                # 不同时间周期使用不同的阈值
                if interval == "5m":
                    threshold = squeeze_threshold * 0.65  # 短周期阈值更低
                elif interval == "15m":
                    threshold = squeeze_threshold * 0.75
                else:  # 1h及以上
                    threshold = squeeze_threshold * 0.9

                configs.append({
                    "symbol": symbol,
                    "interval": interval,
                    "threshold": threshold
                })

        logger.info(f"📋 从配置加载: {len(symbols)}个币种, {len(intervals)}个时间间隔, BB周期{bb_period}, 标准差{bb_std}, 收口阈值{squeeze_threshold}%")

        # 创建任务列表
        tasks = []
        for i, config in enumerate(configs):
            task = asyncio.create_task(
                check_bollinger_squeeze_and_notify(
                    exchange,
                    config["symbol"],
                    config["interval"],
                    config["threshold"]
                ),
                name=f"{current_file_name}_{i}"
            )
            tasks.append(task)
            await asyncio.sleep(0.5)  # 延迟 0.5 秒后启动下一个任务

        # 等待所有任务完成
        await asyncio.gather(*tasks)


    except CustomError as e:
        reason = f"{str(e)}"
    except asyncio.CancelledError:
        # 任务被取消，输出换行符避免与^C共行
        print_ctrl_c_newline()
        reason = f"主动取消"
    except Exception as e:
        logger.error(f"报错: {traceback.format_exc()}")
        reason = f"报错: {repr(e)}"
    finally:
        # 显示 `reason` 的默认值，以防止未设置导致的问题
        if 'reason' not in locals():  # 判断 reason 是否被赋值
            reason = "执行结束"
        # 发送Telegram消息
        try:
            await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"停止！原因: {reason}")
        except Exception as e:
            logger.error(f"发送Telegram消息失败: {e}")
        # exchange由全局管理，无需在此处关闭
        logger.info(f"{current_file_name} 停止！原因: {reason}")
        return {"task_name":current_file_name, "task_result":reason}

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        # 任务被取消，输出换行符避免与^C共行
        print_ctrl_c_newline()
        logger.info(f"主动取消")
    finally:
        # 程序级清理（关闭exchange）- 只在独立运行时执行
        from utils.exchange_manager import close_global_exchange
        try:
            asyncio.run(close_global_exchange())
            logger.info("✅ Exchange连接已关闭")
        except Exception as e:
            logger.error(f"Exchange关闭时出错: {e}")