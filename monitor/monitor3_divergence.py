"""监控：价格和MACD指标背离"""
# 只提醒，不自动操作。收到提醒后开始重点观察此币种，等待机会手动操作。

import sys
import asyncio
import pandas as pd
import traceback
import time
from dotenv import load_dotenv
import os
from ccxt.base.errors import ExchangeNotAvailable,RequestTimeout,NetworkError

# 添加项目根目录到 sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.utils_logger import setup_logger
from errors import CustomError
from utils.utils_telegram import send_telegram_message
from utils.utils_exchange_error import handle_exchange_error
from constants import TASK_DIVIDER, EXCHANGE_DOWN_WAITING
from utils.utils_common import parse_interval, get_sleep_time, format_time, format_dataframe_with_tabulate, print_ctrl_c_newline,get_closed_candle_index
from utils.exchange_wrapper import create_exchange_wrapper
from utils.config_manager import get_monitor_config

# 初始化日志记录器
current_file_name = os.path.splitext(os.path.basename(__file__))[0]
log_name = f"{current_file_name}_logger"
log_file_name = f"{current_file_name}.log"
logger = setup_logger(log_name=log_name,log_file=log_file_name)

# 加载.env文件, 读取bot_token和chat_id
load_dotenv()
TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN_MONITOR")
TELEGRAM_CHAT_ID = os.getenv("TELEGRAM_CHAT_ID")

# 使用全局exchange包装器
exchange = create_exchange_wrapper(current_file_name, logger)

#  ******** 👇下面的数据都不要修改👇 ********

# 定义全局变量: 交易所是否宕机状态
exchange_is_down = False

# 定义常量: 
CHN_NAME = f"MACD背离"  # 中文名称

#  ******** 👆上面的数据都不要修改👆 ********

# 获取历史 K 线数据
async def fetch_ohlcv(symbol, interval='1h', limit=99):
    data = await exchange.fetch_ohlcv(symbol, interval, limit=limit)
    return pd.DataFrame(data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

# 计算 EMA
def ema(prices, period):
    return prices.ewm(span=period, adjust=False).mean()

# 计算 MACD 和信号
def calculate_macd(data, fast_length=12, slow_length=26, signal_length=9):
    data['fast_ema'] = ema(data['close'], fast_length)
    data['slow_ema'] = ema(data['close'], slow_length)
    data['macd'] = data['fast_ema'] - data['slow_ema']
    data['signal'] = ema(data['macd'], signal_length)
    data['delta'] = data['macd'] - data['signal']

    # 保留两位小数
    data['fast_ema'] = data['fast_ema'].round(2)
    data['slow_ema'] = data['slow_ema'].round(2)
    data['macd'] = data['macd'].round(2)
    data['signal'] = data['signal'].round(2)
    data['delta'] = data['delta'].round(2)

    return data

# 检测背离和信号消失
def detect_divergences(data, backlen):
    data['lowest_price'] = data['close'].rolling(window=backlen).min()
    data['highest_price'] = data['close'].rolling(window=backlen).max()
    
    # 底背离（钝化）
    data['div_long'] = (
        (data['macd'] < 0) &
        (data['lowest_price'] == data['close']) &
        ((data['macd'].rolling(window=backlen).min() < data['macd']) | (data['delta'].rolling(window=backlen).min() < data['delta'])) &
        (data['delta'] < 0) &
        (data['delta'].shift(1) < 0)
    ).astype(int)

    # 顶背离（钝化）
    data['div_short'] = (
        (data['macd'] > 0) &
        (data['highest_price'] == data['close']) &
        ((data['macd'].rolling(window=backlen).max() > data['macd']) | (data['delta'].rolling(window=backlen).max() > data['delta'])) &
        (data['delta'] > 0) &
        (data['delta'].shift(1) > 0)
    ).astype(int)

    # 买点（底部结构）
    data['buy'] = (
        (data['macd'] < 0) &
        (data['delta'] < 0) &
        (data['macd'] > data['macd'].shift(1))
    ).astype(int)

    # 卖点（顶部结构）
    data['sell'] = (
        (data['macd'] > 0) &
        (data['delta'] > 0) &
        (data['macd'] < data['macd'].shift(1))
    ).astype(int)

    # 检测信号消失
    data['buy_gone'] = (
        (data['macd'].shift(1) < 0) &
        (data['delta'].shift(1) < 0) &
        (data['macd'].shift(1) > data['macd'].shift(2)) &
        (data['macd'] < data['macd'].shift(1))
    ).astype(int)

    data['div_long_gone'] = (
        (data['macd'].shift(1) < 0) &
        (data['lowest_price'].shift(1) == data['close'].shift(1)) &
        ((data['macd'].rolling(window=backlen).min().shift(1) < data['macd'].shift(1)) | (data['delta'].rolling(window=backlen).min().shift(1) < data['delta'].shift(1))) &
        (data['delta'].shift(1) < 0) &
        (data['delta'].shift(2) < 0) &
        (data['macd'] >= data['macd'].shift(1))
    ).astype(int)

    data['sell_gone'] = (
        (data['macd'].shift(1) > 0) &
        (data['delta'].shift(1) > 0) &
        (data['macd'].shift(1) < data['macd'].shift(2)) &
        (data['macd'] > data['macd'].shift(1))
    ).astype(int)

    data['div_short_gone'] = (
        (data['macd'].shift(1) > 0) &
        (data['highest_price'].shift(1) == data['close'].shift(1)) &
        ((data['macd'].rolling(window=backlen).max().shift(1) > data['macd'].shift(1)) | (data['delta'].rolling(window=backlen).max().shift(1) > data['delta'].shift(1))) &
        (data['delta'].shift(1) > 0) &
        (data['delta'].shift(2) > 0) &
        (data['macd'] <= data['macd'].shift(1))
    ).astype(int)

    return data


async def check_divergence_and_notify(exchange, symbol, interval):
    global exchange_is_down
    while True:
        sub_msg = f"检查 {symbol} 的 {interval} {CHN_NAME}"
        if not exchange_is_down:
            logger.info(f"定时{sub_msg}")
        else:
            msg = f"重试!\n{sub_msg}..."
            logger.info(msg.replace("\n"," "))
            # send_email(f"{current_file_name} 重试", msg)
            # 发送Telegram消息（现在有限流保护）
            try:
                await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, msg)
            except Exception as e:
                logger.error(f"发送Telegram消息失败: {e}")
        try:
            ohlcv = await fetch_ohlcv(symbol, interval)
            ohlcv['close'] = ohlcv['close'].astype(float)

            ohlcv = calculate_macd(ohlcv, fast_length=12, slow_length=26, signal_length=9)
            ohlcv = detect_divergences(ohlcv, backlen=30)
            ohlcv['timestamp'] = pd.to_datetime(ohlcv['timestamp'], unit='ms') + pd.Timedelta(hours=8)  # +8小时, 就是北京时间了
            logger.debug(f"历史K线数据表如下:\n" + format_dataframe_with_tabulate(ohlcv.tail(20), indent=32))

            if exchange_is_down:
                exchange_is_down = False
                # 交易所服务正常了
                e_msg = f"✅ 交易所服务正常了"
                logger.info(f"{current_file_name} {e_msg}")
                # send_email(f"{current_file_name} ✅ 交易所服务正常了", {e_msg})
                # 发送Telegram消息（现在有限流保护）
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, e_msg)
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

            # 动态获取已收盘 K 线索引
            try:
                closed_candle_idx = get_closed_candle_index(ohlcv, interval)
                logger.debug(f"索引为：{closed_candle_idx}")
                last_row = ohlcv.iloc[closed_candle_idx]
            except Exception as e:
                logger.error(f"处理K线数据时出错: {e}")
                continue  # 跳过本次循环，继续下一次检查

            base_msg = (f"币种: {symbol}\n"
                       f"时间: {last_row['timestamp']}\n"
                       f"周期: {interval}\n"
                       f"收盘价: {last_row['close']}")

            # 输出信号
            if last_row['div_long']:
                msg = f"⚠️ 底背离\n{base_msg}"
                logger.info(msg.replace("\n",", "))
                # 发送消息
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"\n{msg}")
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")
            
            if last_row['div_short']:
                msg = f"⚠️ 顶背离\n{base_msg}"
                logger.info(msg.replace("\n",", "))
                # 发送消息
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"\n{msg}")
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

            if last_row['buy']:
                msg = f"⚠️ 买点\n{base_msg}"
                logger.info(msg.replace("\n",", "))
                # 发送消息
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"\n{msg}")
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

            if last_row['sell']:
                msg = f"⚠️ 卖点\n{base_msg}"
                logger.info(msg.replace("\n",", "))
                # 发送消息
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"\n{msg}")
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

            if last_row['buy_gone']:
                msg = f"⚠️ 买点消失\n{base_msg}"
                logger.info(msg.replace("\n",", "))
                # 发送消息
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"\n{msg}")
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

            if last_row['div_long_gone']:
                msg = f"⚠️ 底背离消失\n{base_msg}"
                logger.info(msg.replace("\n",", "))
                # 发送消息
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"\n{msg}")
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

            if last_row['sell_gone']:
                msg = f"⚠️ 卖点消失\n{base_msg}"
                logger.info(msg.replace("\n",", "))
                # 发送消息
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"\n{msg}")
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

            if last_row['div_short_gone']:
                msg = f"⚠️ 顶背离消失\n{base_msg}"
                logger.info(msg.replace("\n",", "))
                # 发送消息
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"\n{msg}")
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

            # 检查成交量是否超过阈值
            # if volume > volume_threshold:
            #     msg = f"⚠️ {symbol}, {time}, 周期 {interval}, 成交量 {formatted_volume}, 超过 {formatted_threshold} !"
            #     logger.info(msg.replace("\n"," "))
            #     # 发送消息
            #     await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"\n{msg}")

            # else:
            #     logger.debug(f"{symbol}, {time}, 周期 {interval}, 成交量 {formatted_volume}, 未超过")
                
        except (ExchangeNotAvailable, RequestTimeout, NetworkError) as e:
            logger.info(f"异常: {e}")
            exchange_is_down = True
            # 交易所服务宕机或不可用, 一会继续重试
            e_msg = f"❎ 交易所服务异常 ({type(e).__name__}), 暂无法 {sub_msg}, {EXCHANGE_DOWN_WAITING} 后重试..."
            logger.info(f"{current_file_name} {e_msg}")
            # send_email(f"{current_file_name} ❎ 交易所服务宕机", {e_msg})
            # 使用防重复发送工具
            await handle_exchange_error(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, e_msg)

            # 随机延迟重试，好像意义不大，因为每个任务复杂度不一样、执行时间也不一样，几乎不会出现同时重试
            wait_time = parse_interval(EXCHANGE_DOWN_WAITING)
            await asyncio.sleep(wait_time)
        except Exception as e:
            logger.error(f"检查过程中出错了:")
            raise e

        if not exchange_is_down:
            # 等待一段时间后再次检查
            # wait_time = 60
            # wait_time = random.randint(60, 90)  # 随机生成60到90秒之间的等待时间
            # wait_time = parse_interval(interval)  # sleep固定间隔
            # ftime = interval
            try:
                current_timestamp = await exchange.fetch_time() # 获取服务器时间（毫秒时间戳）
            except (ExchangeNotAvailable, RequestTimeout, NetworkError) as e:
                logger.warning(f"获取服务器时间失败: {e}, 使用本地时间")
                # 如果获取服务器时间失败，使用本地时间（毫秒时间戳）
                current_timestamp = int(time.time() * 1000)
            except Exception as e:
                logger.error(f"获取服务器时间时发生未知错误: {e}, 使用本地时间")
                current_timestamp = int(time.time() * 1000)
            
            # 根据 interval 动态调整 sleep 时间
            try:
                wait_time = get_sleep_time(current_timestamp, interval)
                ftime = format_time(wait_time)
            except Exception as e:
                logger.error(f"计算等待时间时出错: {e}, 使用默认间隔")
                wait_time = parse_interval(interval)
                ftime = format_time(wait_time)
            logger.info(f"等待 {ftime} 后再次{sub_msg}...")
            await asyncio.sleep(wait_time)  # 每隔一段时间检查一次

            # time.sleep(sleep_time)  # 等待所需的时间

async def main():

    logger.info(f"{current_file_name} 开始！")
    # send_email(f"{current_file_name} 开始", "启动啦")
    # 发送Telegram消息
    try:
        await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"开始！")
    except Exception as e:
        logger.error(f"发送Telegram消息失败: {e}")

    try:

        # 只检查一组数据
        # await check_divergence_and_notify(exchange, trade_symbol, timeframe, backlen)

        # 从配置文件获取监控配置
        symbols = get_monitor_config('monitor.common.symbols', ["BTC/USDT:USDT"])
        intervals = get_monitor_config('monitor.common.intervals', ["15m", "1h", "4h", "1d"])
        min_divergence_bars = get_monitor_config('monitor.divergence.min_divergence_bars', 5)

        # 构建配置列表
        configs = []
        for symbol in symbols:
            for interval in intervals:
                # backlen 用于获取足够的历史数据进行背离分析
                backlen = max(30, min_divergence_bars * 6)  # 确保有足够的数据
                configs.append({
                    "symbol": symbol,
                    "interval": interval,
                    "backlen": backlen
                })

        logger.info(f"📋 从配置加载: {len(symbols)}个币种, {len(intervals)}个时间间隔, 最小背离K线数{min_divergence_bars}")

        # 创建任务列表
        tasks = []
        for i,config in enumerate(configs):
            task = asyncio.create_task(check_divergence_and_notify(exchange, config["symbol"], config["interval"]),name=f"{current_file_name}_{i}")
            tasks.append(task)
            await asyncio.sleep(0.5)  # 延迟 0.5 秒后启动下一个任务

        # 等待所有任务完成
        await asyncio.gather(*tasks)
        

    except CustomError as e:
        reason = f"{str(e)}"
    except asyncio.CancelledError:
        # 任务被取消，输出换行符避免与^C共行
        print_ctrl_c_newline()
        reason = f"主动取消"
    except Exception as e:
        logger.error(f"报错: {traceback.format_exc()}")
        reason = f"报错: {repr(e)}"
    finally:
        # 显示 `reason` 的默认值，以防止未设置导致的问题
        if 'reason' not in locals():  # 判断 reason 是否被赋值
            reason = "执行结束"
        # send_email(f"{current_file_name} 停止", f"{reason}")
        # 发送Telegram消息
        try:
            await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"停止！原因: {reason}")
        except Exception as e:
            logger.error(f"发送Telegram消息失败: {e}")
        # exchange由全局管理，无需在此处关闭
        logger.info(f"{current_file_name} 停止！原因: {reason}")
        return {"task_name":current_file_name, "task_result":reason}

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        # 任务被取消，输出换行符避免与^C共行
        print_ctrl_c_newline()
        logger.info(f"主动取消")
    finally:
        # 程序级清理（关闭exchange）- 只在独立运行时执行
        from utils.exchange_manager import close_global_exchange
        # 在新的事件循环中关闭exchange
        try:
            asyncio.run(close_global_exchange())
            logger.info("✅ Exchange连接已关闭")
        except Exception as e:
            logger.error(f"Exchange关闭时出错: {e}")