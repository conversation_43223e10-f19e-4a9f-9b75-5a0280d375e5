"""监控：成交量与持仓量关系矩阵分析"""
# 根据成交量和持仓量的变化组合，分析市场状态并发送相应的操作建议

import sys
import asyncio
import traceback
import time
import pandas as pd
from dotenv import load_dotenv
import os
from ccxt.base.errors import ExchangeNotAvailable, RequestTimeout, NetworkError

# 添加项目根目录到 sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.utils_logger import setup_logger
from utils.errors import CustomError
from utils.utils_telegram import send_telegram_message
from utils.utils_exchange_error import handle_exchange_error
from utils.constants import TASK_DIVIDER, EXCHANGE_DOWN_WAITING
from utils.utils_common import parse_interval, format_time, get_sleep_time, print_ctrl_c_newline
from utils.exchange_wrapper import create_exchange_wrapper
from utils.config_manager import get_monitor_config

# 初始化日志记录器
current_file_name = os.path.splitext(os.path.basename(__file__))[0]
log_name = f"{current_file_name}_logger"
log_file_name = f"{current_file_name}.log"
logger = setup_logger(log_name=log_name,log_file=log_file_name)

# 加载.env文件, 读取bot_token和chat_id
load_dotenv()
TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN_MONITOR")
TELEGRAM_CHAT_ID = os.getenv("TELEGRAM_CHAT_ID")

# 使用全局exchange包装器
exchange = create_exchange_wrapper(current_file_name, logger)

CHN_NAME = "成交量持仓量关系矩阵分析"

# 全局状态变量
exchange_is_down = False
symbol_states = {}

#  ******** 👆上面的数据都不要修改👆 ********

async def fetch_ohlcv(symbol, interval, limit=100):
    """获取K线数据"""
    try:
        ohlcv = await exchange.fetch_ohlcv(symbol, interval, limit=limit)
        df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms') + pd.Timedelta(hours=8)  # +8小时, 就是北京时间了
        return df
    except Exception as e:
        logger.error(f"获取K线数据失败: {e}")
        raise e

class VolumeOIState:
    """成交量和持仓量状态类"""
    def __init__(self):
        self.volume_history = []  # 成交量历史
        self.oi_history = []      # 持仓量历史
        self.price_history = []   # 价格历史
        
    def add_data(self, volume, oi, timestamp, price=None):
        """添加新数据点，如果timestamp相同则更新，否则新增"""
        # 检查是否已存在相同timestamp的数据
        volume_updated = False
        oi_updated = False
        price_updated = False

        # 更新成交量数据
        for i, data in enumerate(self.volume_history):
            if data['timestamp'] == timestamp:
                self.volume_history[i] = {'value': volume, 'timestamp': timestamp}
                volume_updated = True
                break

        # 更新持仓量数据
        for i, data in enumerate(self.oi_history):
            if data['timestamp'] == timestamp:
                self.oi_history[i] = {'value': oi, 'timestamp': timestamp}
                oi_updated = True
                break

        # 更新价格数据
        if price is not None:
            for i, data in enumerate(self.price_history):
                if data['timestamp'] == timestamp:
                    self.price_history[i] = {'value': price, 'timestamp': timestamp}
                    price_updated = True
                    break

        # 如果没有找到相同timestamp，则添加新数据
        if not volume_updated:
            self.volume_history.append({'value': volume, 'timestamp': timestamp})
        if not oi_updated:
            self.oi_history.append({'value': oi, 'timestamp': timestamp})
        if price is not None and not price_updated:
            self.price_history.append({'value': price, 'timestamp': timestamp})

        # 只保留最近10个数据点，避免内存无限增长
        if len(self.volume_history) > 10:
            self.volume_history = self.volume_history[-10:]
        if len(self.oi_history) > 10:
            self.oi_history = self.oi_history[-10:]
        if len(self.price_history) > 10:
            self.price_history = self.price_history[-10:]

def calculate_change_percentage(current, previous):
    """计算变化百分比"""
    if previous == 0:
        return 0
    return ((current - previous) / previous) * 100

def analyze_volume_oi_matrix(state, current_volume, current_oi, timestamp, current_price, volume_threshold=50.0, oi_threshold=3.0):
    """
    分析成交量与持仓量关系矩阵

    注意：成交量和持仓量的性质不同
    - 成交量：当前周期内的成交量（增量值），波动较大
    - 持仓量：当前时刻的总持仓量（存量值），变化相对平缓

    Args:
        state: VolumeOIState对象
        current_volume: 当前周期成交量（增量）
        current_oi: 当前总持仓量（存量）
        timestamp: 当前时间戳
        current_price: 当前价格
        volume_threshold: 成交量变化阈值（百分比），默认50%
        oi_threshold: 持仓量变化阈值（百分比），默认3%

    Returns:
        dict: 分析结果
    """
    # 至少需要2个数据点才能比较（当前 + 上一次）
    if len(state.volume_history) < 2 or len(state.oi_history) < 2 or len(state.price_history) < 2:
        return None

    # 根据当前timestamp找到对应的历史数据位置
    current_volume_idx = None
    current_oi_idx = None
    current_price_idx = None

    # 在成交量历史中找到当前timestamp的位置
    for i, data in enumerate(state.volume_history):
        if data['timestamp'] == timestamp:
            current_volume_idx = i
            break

    # 在持仓量历史中找到当前timestamp的位置
    for i, data in enumerate(state.oi_history):
        if data['timestamp'] == timestamp:
            current_oi_idx = i
            break

    # 在价格历史中找到当前timestamp的位置
    for i, data in enumerate(state.price_history):
        if data['timestamp'] == timestamp:
            current_price_idx = i
            break

    # 如果找不到当前数据或者没有前一个数据，则无法比较
    if (current_volume_idx is None or current_volume_idx == 0 or
        current_oi_idx is None or current_oi_idx == 0 or
        current_price_idx is None or current_price_idx == 0):
        return None

    # 获取前一个数据
    previous_volume = state.volume_history[current_volume_idx - 1]['value']
    previous_oi = state.oi_history[current_oi_idx - 1]['value']
    previous_price = state.price_history[current_price_idx - 1]['value']

    # 打印调试信息
    logger.info(f"分析数据 - current_volume: {current_volume}, current_volume_idx: {current_volume_idx}, previous_volume: {previous_volume}")
    logger.info(f"分析数据 - current_oi: {current_oi}, current_oi_idx: {current_oi_idx}, previous_oi: {previous_oi}")
    logger.info(f"分析数据 - current_price: {current_price}, current_price_idx: {current_price_idx}, previous_price: {previous_price}")

    # 计算变化百分比
    volume_change = calculate_change_percentage(current_volume, previous_volume)
    oi_change = calculate_change_percentage(current_oi, previous_oi)
    price_change = calculate_change_percentage(current_price, previous_price)

    logger.info(f"变化百分比 - volume_change: {volume_change:.2f}%, oi_change: {oi_change:.2f}%, price_change: {price_change:.2f}%")
    
    # 使用传入的阈值参数
    # 判断成交量和持仓量的变化方向
    volume_up = volume_change > volume_threshold
    volume_down = volume_change < -volume_threshold
    oi_up = oi_change > oi_threshold
    oi_down = oi_change < -oi_threshold
    
    # 根据关系矩阵确定信号类型
    signal_type = None
    market_meaning = ""
    operation_advice = ""
    emoji = ""
    
    # 判断价格趋势方向
    is_uptrend = price_change > 0  # 价格上涨为上升趋势

    if volume_up and oi_up:
        signal_type = "trend_continuation"
        market_meaning = "新资金入场，趋势延续"
        if is_uptrend:
            operation_advice = "顺势做多"
            emoji = "🚀"  # 上涨趋势延续
        else:
            operation_advice = "顺势做空"
            emoji = "📉"  # 下跌趋势延续
    elif volume_up and oi_down:
        signal_type = "trend_exhaustion"
        market_meaning = "旧头寸平仓，趋势衰竭"
        if is_uptrend:
            operation_advice = "反转做空"
            emoji = "📉"  # 上涨衰竭，准备下跌
        else:
            operation_advice = "反转做多"
            emoji = "🚀"  # 下跌衰竭，准备上涨
    elif volume_down and oi_up:
        signal_type = "market_standoff"
        market_meaning = "多空对峙，方向选择前夜"
        operation_advice = "保持观望"
        emoji = "👀"
    elif volume_down and oi_down:
        signal_type = "market_quiet"
        market_meaning = "市场冷清，缺乏方向"
        operation_advice = "停止开新仓"
        emoji = "😴"
    else:
        # 变化不显著，不发送信号
        return None
    
    return {
        'signal_type': signal_type,
        'market_meaning': market_meaning,
        'operation_advice': operation_advice,
        'emoji': emoji,
        'volume_change': volume_change,
        'oi_change': oi_change,
        'current_volume': current_volume,
        'current_oi': current_oi,
        'previous_volume': previous_volume,
        'previous_oi': previous_oi
    }



def format_open_interest(oi_value):
    """格式化持仓量显示"""
    if oi_value >= 1e9:
        return f"{oi_value/1e9:.2f}B"
    elif oi_value >= 1e6:
        return f"{oi_value/1e6:.2f}M"
    elif oi_value >= 1e3:
        return f"{oi_value/1e3:.2f}K"
    else:
        return f"{oi_value:.2f}"

def format_volume(volume_value):
    """格式化成交量显示"""
    if volume_value >= 1e9:
        return f"{volume_value/1e9:.2f}B"
    elif volume_value >= 1e6:
        return f"{volume_value/1e6:.2f}M"
    elif volume_value >= 1e3:
        return f"{volume_value/1e3:.2f}K"
    else:
        return f"{volume_value:.2f}"

async def fetch_volume_and_oi_data(symbol, interval):
    """获取成交量和持仓量数据"""
    try:
        # 获取K线数据（包含成交量）
        ohlcv = await fetch_ohlcv(symbol, interval, limit=2)

        # 获取持仓量数据
        oi_data = await exchange.fetch_open_interest(symbol)
        current_oi = float(oi_data['openInterestAmount'])

        # 动态获取已收盘 K 线索引（类似monitor1的做法）
        try:
            from utils.utils_common import get_closed_candle_index
            closed_candle_idx = get_closed_candle_index(ohlcv, interval)
            logger.debug(f"已收盘K线索引为：{closed_candle_idx}")

            # 获取已收盘K线的成交量、价格和时间戳
            current_volume = float(ohlcv.iloc[closed_candle_idx]['volume'])
            current_close = float(ohlcv.iloc[closed_candle_idx]['close'])
            current_timestamp = ohlcv.iloc[closed_candle_idx]['timestamp']  # 直接使用北京时间的datetime对象
        except Exception as e:
            logger.error(f"处理K线数据时出错: {e}")
            # 如果获取已收盘K线失败，使用最新K线
            current_volume = float(ohlcv.iloc[-1]['volume'])
            current_close = float(ohlcv.iloc[-1]['close'])
            current_timestamp = ohlcv.iloc[-1]['timestamp']

        return current_volume, current_oi, current_timestamp, current_close

    except Exception as e:
        logger.error(f"获取成交量和持仓量数据失败: {e}")
        raise e


async def initialize_historical_data(state, symbol, interval):
    """初始化历史数据，获取最近几个周期的数据"""
    try:
        logger.info(f"正在初始化 {symbol} 的历史数据...")

        # 获取最近10个周期的K线数据
        ohlcv = await fetch_ohlcv(symbol, interval, limit=10)

        # 获取当前持仓量（假设持仓量在短期内变化不大，用当前值填充历史）
        oi_data = await exchange.fetch_open_interest(symbol)
        current_oi = float(oi_data['openInterestAmount'])

        # 将历史数据添加到状态中
        for i in range(len(ohlcv)):
            volume = float(ohlcv.iloc[i]['volume'])
            close_price = float(ohlcv.iloc[i]['close'])
            timestamp = ohlcv.iloc[i]['timestamp']  # 直接使用北京时间的datetime对象
            # 对于持仓量，我们使用当前值（因为历史持仓量数据不容易获取）
            state.add_data(volume, current_oi, timestamp, close_price)

        logger.info(f"{symbol} 历史数据初始化完成，共 {len(ohlcv)} 个数据点")

    except Exception as e:
        logger.error(f"初始化 {symbol} 历史数据失败: {e}")
        # 如果初始化失败，不抛出异常，让程序继续运行
        pass


async def check_volume_oi_matrix_and_notify(exchange, symbol, interval, volume_threshold=50.0, oi_threshold=3.0):
    global exchange_is_down, symbol_states

    # 使用symbol+interval作为状态键，避免同一symbol不同interval的状态冲突
    state_key = f"{symbol}_{interval}"

    # 初始化币种状态
    if state_key not in symbol_states:
        symbol_states[state_key] = VolumeOIState()
        # 初始化历史数据，这样就可以立即开始分析
        await initialize_historical_data(symbol_states[state_key], symbol, interval)

    state = symbol_states[state_key]

    while True:
        sub_msg = f"检查 {symbol} 的 {interval} {CHN_NAME}"
        if not exchange_is_down:
            logger.info(f"定时{sub_msg}")
        else:
            msg = f"重试!\n{sub_msg}..."
            logger.info(msg.replace("\n"," "))
            # 发送Telegram消息（现在有限流保护）
            try:
                await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, msg)
            except Exception as e:
                logger.error(f"发送Telegram消息失败: {e}")

        try:
            # 获取成交量和持仓量数据
            current_volume, current_oi, current_timestamp, current_close = await fetch_volume_and_oi_data(symbol, interval)

            # 添加数据到状态
            state.add_data(current_volume, current_oi, current_timestamp, current_close)

            logger.info(f"当前数据 - 成交量: {format_volume(current_volume)}, 持仓量: {format_open_interest(current_oi)}")

            # 打印历史数据用于调试
            volume_history_str = [f"{format_volume(d['value'])}@{d['timestamp'].strftime('%H:%M')}" for d in state.volume_history[-3:]]
            oi_history_str = [f"{format_open_interest(d['value'])}@{d['timestamp'].strftime('%H:%M')}" for d in state.oi_history[-3:]]
            logger.info(f"成交量历史 ({len(state.volume_history)}个): {volume_history_str}")
            logger.info(f"持仓量历史 ({len(state.oi_history)}个): {oi_history_str}")

            if exchange_is_down:
                exchange_is_down = False
                # 交易所服务正常了
                e_msg = f"✅ 交易所服务正常了"
                logger.info(f"{current_file_name} {e_msg}")
                # 发送Telegram消息（由于exchange_is_down的全局状态特性，只有第一个任务会进入这里）
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, e_msg)
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

            # 分析成交量与持仓量关系矩阵
            analysis_result = analyze_volume_oi_matrix(state, current_volume, current_oi, current_timestamp, current_close, volume_threshold, oi_threshold)

            if analysis_result:

                # 格式化时间（current_timestamp已经是北京时间的datetime对象）
                formatted_time = current_timestamp.strftime('%Y-%m-%d %H:%M:%S')

                # 构建消息
                msg = (f"{analysis_result['emoji']} {analysis_result['market_meaning']}\n"
                      f"币种: {symbol}\n"
                      f"时间: {formatted_time}\n"
                      f"周期: {interval}\n"
                    #   f"━━━━━━━━━━━━━━━━━━━━\n"
                      f"📊 数据分析:\n"
                      f"成交量: {format_volume(current_volume)} ({analysis_result['volume_change']:+.1f}%)\n"
                      f"持仓量: {format_open_interest(current_oi)} ({analysis_result['oi_change']:+.1f}%)\n"
                    #   f"━━━━━━━━━━━━━━━━━━━━\n"
                      f"💡 操作建议: {analysis_result['operation_advice']}")

                logger.info(msg.replace("\n", ", "))

                # 发送Telegram消息
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"\n{msg}")
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

        except (ExchangeNotAvailable, RequestTimeout, NetworkError) as e:
            logger.info(f"异常: {e}")
            exchange_is_down = True
            # 交易所服务宕机或不可用, 一会继续重试
            e_msg = f"❎ 交易所服务异常 ({type(e).__name__}), 暂无法 {sub_msg}, {EXCHANGE_DOWN_WAITING} 后重试..."
            logger.info(f"{current_file_name} {e_msg}")
            # 使用防重复发送工具
            await handle_exchange_error(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, e_msg)

            # 随机延迟重试，好像意义不大，因为每个任务复杂度不一样、执行时间也不一样，几乎不会出现同时重试
            wait_time = parse_interval(EXCHANGE_DOWN_WAITING)
            await asyncio.sleep(wait_time)
        except Exception as e:
            logger.error(f"检查过程中出错了:")
            raise e

        if not exchange_is_down:
            # 等待一段时间后再次检查
            try:
                current_timestamp = await exchange.fetch_time() # 获取服务器时间（毫秒时间戳）
            except (ExchangeNotAvailable, RequestTimeout, NetworkError) as e:
                logger.warning(f"获取服务器时间失败: {e}, 使用本地时间")
                # 如果获取服务器时间失败，使用本地时间（毫秒时间戳）
                current_timestamp = int(time.time() * 1000)
            except Exception as e:
                logger.error(f"获取服务器时间时发生未知错误: {e}, 使用本地时间")
                current_timestamp = int(time.time() * 1000)

            # 根据 interval 动态调整 sleep 时间
            try:
                wait_time = get_sleep_time(current_timestamp, interval)
                ftime = format_time(wait_time)
            except Exception as e:
                logger.error(f"计算等待时间时出错: {e}, 使用默认间隔")
                wait_time = parse_interval(interval)
                ftime = format_time(wait_time)
            logger.info(f"等待 {ftime} 后再次{sub_msg}...")
            await asyncio.sleep(wait_time)  # 每隔一段时间检查一次

async def main():

    logger.info(f"{current_file_name} 开始！")
    # 发送Telegram消息
    try:
        await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"开始！")
    except Exception as e:
        logger.error(f"发送Telegram消息失败: {e}")

    try:

        # 从配置文件获取监控配置
        symbols = get_monitor_config('monitor.common.symbols', ["BTC/USDT:USDT"])
        intervals = get_monitor_config('monitor.common.intervals', ["5m", "15m"])
        volume_threshold = get_monitor_config('monitor.volume_oi_matrix.volume_threshold', 1.5)
        oi_threshold = get_monitor_config('monitor.volume_oi_matrix.oi_threshold', 2.0)

        # 构建配置列表
        configs = []
        for symbol in symbols:
            for interval in intervals:
                configs.append({
                    "symbol": symbol,
                    "interval": interval,
                    "volume_threshold": volume_threshold,
                    "oi_threshold": oi_threshold
                })

        logger.info(f"📋 从配置加载: {len(symbols)}个币种, {len(intervals)}个时间间隔, 成交量阈值{volume_threshold}倍, OI阈值{oi_threshold}%")

        # 创建任务列表
        tasks = []
        for i,config in enumerate(configs):
            task = asyncio.create_task(
                check_volume_oi_matrix_and_notify(
                    exchange,
                    config["symbol"],
                    config["interval"],
                    config["volume_threshold"],
                    config["oi_threshold"]
                ),
                name=f"{current_file_name}_{i}"
            )
            tasks.append(task)
            await asyncio.sleep(0.5)  # 延迟 0.5 秒后启动下一个任务

        # 等待所有任务完成
        await asyncio.gather(*tasks)


    except CustomError as e:
        reason = f"{str(e)}"
    except asyncio.CancelledError:
        # 任务被取消，输出换行符避免与^C共行
        print_ctrl_c_newline()
        reason = f"主动取消"
    except Exception as e:
        logger.error(f"报错: {traceback.format_exc()}")
        reason = f"报错: {repr(e)}"
    finally:
        # 显示 `reason` 的默认值，以防止未设置导致的问题
        if 'reason' not in locals():  # 判断 reason 是否被赋值
            reason = "执行结束"
        # 发送Telegram消息
        try:
            await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"停止！原因: {reason}")
        except Exception as e:
            logger.error(f"发送Telegram消息失败: {e}")
        # exchange由全局管理，无需在此处关闭
        logger.info(f"{current_file_name} 停止！原因: {reason}")
        return {"task_name":current_file_name, "task_result":reason}

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        # 任务被取消，输出换行符避免与^C共行
        print_ctrl_c_newline()
        logger.info(f"主动取消")
    finally:
        # 程序级清理（关闭exchange）- 只在独立运行时执行
        from utils.exchange_manager import close_global_exchange
        try:
            asyncio.run(close_global_exchange())
            logger.info("✅ Exchange连接已关闭")
        except Exception as e:
            logger.error(f"Exchange关闭时出错: {e}")
