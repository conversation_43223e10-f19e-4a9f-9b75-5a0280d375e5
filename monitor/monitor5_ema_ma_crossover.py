"""监控：EMA20与MA170交叉后的回踩和反转形态检测"""
# EMA20与MA170金叉后，价格回踩MA170时发出观察信号，出现反转看涨形态时发出买入信号
# EMA20与MA170死叉后，价格回踩EMA20时发出观察信号，出现反转看跌形态时发出卖出信号

import sys
import asyncio
import pandas as pd
import traceback
import time
from dotenv import load_dotenv
import os
from ccxt.base.errors import ExchangeNotAvailable,RequestTimeout,NetworkError

# 添加项目根目录到 sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.utils_logger import setup_logger
from errors import CustomError
from utils.utils_telegram import send_telegram_message
from utils.utils_exchange_error import handle_exchange_error
from constants import TASK_DIVIDER, EXCHANGE_DOWN_WAITING
from utils.utils_common import parse_interval, get_sleep_time, format_time, format_dataframe_with_tabulate,get_closed_candle_index, print_ctrl_c_newline
from utils.exchange_wrapper import create_exchange_wrapper
from utils.config_manager import get_monitor_config

# 初始化日志记录器
current_file_name = os.path.splitext(os.path.basename(__file__))[0]
log_name = f"{current_file_name}_logger"
log_file_name = f"{current_file_name}.log"
logger = setup_logger(log_name=log_name,log_file=log_file_name)

# 加载.env文件, 读取bot_token和chat_id
load_dotenv()
TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN_MONITOR")
TELEGRAM_CHAT_ID = os.getenv("TELEGRAM_CHAT_ID")

# 使用全局exchange包装器
exchange = create_exchange_wrapper(current_file_name, logger)

#  ******** 👇下面的数据都不要修改👇 ********

# 定义全局变量: 交易所是否宕机状态
exchange_is_down = False

# 定义常量: 
CHN_NAME = f"EMA20与MA170交叉回踩"  # 中文名称

# 存储每个币种的状态
symbol_states = {}

#  ******** 👆上面的数据都不要修改👆 ********

# 获取历史 K 线数据
async def fetch_ohlcv(symbol, interval='1h', limit=200):
    data = await exchange.fetch_ohlcv(symbol, interval, limit=limit)
    return pd.DataFrame(data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

# 计算 EMA
def ema(prices, period):
    return prices.ewm(span=period, adjust=False).mean()

# 计算 SMA (简单移动平均线)
def sma(prices, period):
    return prices.rolling(window=period).mean()

# 计算技术指标
def calculate_indicators(data):
    data['ema20'] = ema(data['close'], 20)
    data['ma170'] = sma(data['close'], 170)
    
    # 保留两位小数
    data['ema20'] = data['ema20'].round(2)
    data['ma170'] = data['ma170'].round(2)
    
    return data

# 检测交叉信号
def detect_crossover(data):
    # 金叉：EMA20从下方穿越MA170
    data['golden_cross'] = (
        (data['ema20'] > data['ma170']) & 
        (data['ema20'].shift(1) <= data['ma170'].shift(1))
    ).astype(int)
    
    # 死叉：EMA20从上方穿越MA170
    data['death_cross'] = (
        (data['ema20'] < data['ma170']) & 
        (data['ema20'].shift(1) >= data['ma170'].shift(1))
    ).astype(int)
    
    return data

# 检测回踩信号
def detect_pullback(data):
    # 金叉后回踩MA170
    data['pullback_to_ma170'] = (
        (data['low'] <= data['ma170'] * 1.005) &  # 价格触及或接近MA170（允许0.5%误差）
        (data['low'] >= data['ma170'] * 0.995) &
        (data['ema20'] > data['ma170'])  # 确保仍在金叉状态
    ).astype(int)
    
    # 死叉后回踩EMA20
    data['pullback_to_ema20'] = (
        (data['high'] >= data['ema20'] * 0.995) &  # 价格触及或接近EMA20（允许0.5%误差）
        (data['high'] <= data['ema20'] * 1.005) &
        (data['ema20'] < data['ma170'])  # 确保仍在死叉状态
    ).astype(int)
    
    return data

# 检测反转形态
def detect_reversal_patterns(data):
    # 计算实体和影线
    data['body'] = abs(data['close'] - data['open'])
    data['upper_shadow'] = data['high'] - data[['open', 'close']].max(axis=1)
    data['lower_shadow'] = data[['open', 'close']].min(axis=1) - data['low']
    data['total_range'] = data['high'] - data['low']
    
    # 看涨反转形态
    # 1. 长下影线（下影线长度 >= 实体的2倍，且下影线 >= 总区间的40%）
    data['long_lower_shadow'] = (
        (data['lower_shadow'] >= data['body'] * 2) &
        (data['lower_shadow'] >= data['total_range'] * 0.4) &
        (data['total_range'] > 0)
    ).astype(int)
    
    # 2. 看涨吞没形态（阳包阴）
    data['bullish_engulfing'] = (
        (data['close'] > data['open']) &  # 当前K线为阳线
        (data['close'].shift(1) < data['open'].shift(1)) &  # 前一K线为阴线
        (data['open'] < data['close'].shift(1)) &  # 当前开盘价低于前一收盘价
        (data['close'] > data['open'].shift(1))  # 当前收盘价高于前一开盘价
    ).astype(int)
    
    # 看跌反转形态
    # 1. 长上影线（上影线长度 >= 实体的2倍，且上影线 >= 总区间的40%）
    data['long_upper_shadow'] = (
        (data['upper_shadow'] >= data['body'] * 2) &
        (data['upper_shadow'] >= data['total_range'] * 0.4) &
        (data['total_range'] > 0)
    ).astype(int)
    
    # 2. 看跌吞没形态（阴包阳）
    data['bearish_engulfing'] = (
        (data['close'] < data['open']) &  # 当前K线为阴线
        (data['close'].shift(1) > data['open'].shift(1)) &  # 前一K线为阳线
        (data['open'] > data['close'].shift(1)) &  # 当前开盘价高于前一收盘价
        (data['close'] < data['open'].shift(1))  # 当前收盘价低于前一开盘价
    ).astype(int)
    
    return data

# 综合检测信号
def detect_signals(data):
    # 看涨信号：金叉后回踩MA170 + 反转看涨形态
    data['bullish_signal'] = (
        data['pullback_to_ma170'] | 
        (data['pullback_to_ma170'].rolling(window=5).sum() > 0)  # 最近5根K线内有回踩
    ) & (
        data['long_lower_shadow'] | data['bullish_engulfing']
    )
    
    # 看跌信号：死叉后回踩EMA20 + 反转看跌形态  
    data['bearish_signal'] = (
        data['pullback_to_ema20'] |
        (data['pullback_to_ema20'].rolling(window=5).sum() > 0)  # 最近5根K线内有回踩
    ) & (
        data['long_upper_shadow'] | data['bearish_engulfing']
    )
    
    return data


async def check_ema_ma_crossover_and_notify(exchange, symbol, interval):
    global exchange_is_down, symbol_states

    # 使用symbol+interval作为状态键，避免同一symbol不同interval的状态冲突
    state_key = f"{symbol}_{interval}"

    # 初始化币种状态
    if state_key not in symbol_states:
        symbol_states[state_key] = {
            'last_golden_cross': None,
            'last_death_cross': None,
            'pullback_observed': False,
            'trend_direction': None  # 'bullish' or 'bearish'
        }

    while True:
        sub_msg = f"检查 {symbol} 的 {interval} {CHN_NAME}"
        if not exchange_is_down:
            logger.info(f"定时{sub_msg}")
        else:
            msg = f"重试!\n{sub_msg}..."
            logger.info(msg.replace("\n"," "))
            # send_email(f"{current_file_name} 重试", msg)
            # 发送Telegram消息（现在有限流保护）
            try:
                await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, msg)
            except Exception as e:
                logger.error(f"发送Telegram消息失败: {e}")

        try:
            ohlcv = await fetch_ohlcv(symbol, interval)
            ohlcv['close'] = ohlcv['close'].astype(float)
            ohlcv['open'] = ohlcv['open'].astype(float)
            ohlcv['high'] = ohlcv['high'].astype(float)
            ohlcv['low'] = ohlcv['low'].astype(float)

            ohlcv = calculate_indicators(ohlcv)
            ohlcv = detect_crossover(ohlcv)
            ohlcv = detect_pullback(ohlcv)
            ohlcv = detect_reversal_patterns(ohlcv)
            ohlcv = detect_signals(ohlcv)

            ohlcv['timestamp'] = pd.to_datetime(ohlcv['timestamp'], unit='ms') + pd.Timedelta(hours=8)  # +8小时, 就是北京时间了
            logger.debug(f"历史K线数据表如下:\n" + format_dataframe_with_tabulate(ohlcv.tail(10), indent=32))

            if exchange_is_down:
                exchange_is_down = False
                # 交易所服务正常了
                e_msg = f"✅ 交易所服务正常了"
                logger.info(f"{current_file_name} {e_msg}")
                # 发送Telegram消息（由于exchange_is_down的全局状态特性，只有第一个任务会进入这里）
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, e_msg)
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

            # 动态获取已收盘 K 线索引
            try:
                closed_candle_idx = get_closed_candle_index(ohlcv, interval)
                logger.debug(f"索引为：{closed_candle_idx}")
                last_row = ohlcv.iloc[closed_candle_idx]
            except Exception as e:
                logger.error(f"处理K线数据时出错: {e}")
                continue  # 跳过本次循环，继续下一次检查

            # 提取币种名称并加粗
            # symbol_parts = symbol.split('/')
            # coin_name = symbol_parts[0]  # 例如 "BTC"
            # rest_of_symbol = symbol[len(coin_name):]  # 例如 "/USDT:USDT"
            # formatted_symbol = f"*{coin_name}*{rest_of_symbol}"

            base_msg = (f"币种: {symbol}\n"
                       f"时间: {last_row['timestamp']}\n"
                       f"周期: {interval}\n"
                       f"收盘价: {last_row['close']}\n"
                       f"EMA20: {last_row['ema20']}\n"
                       f"MA170: {last_row['ma170']}")

            state = symbol_states[state_key]

            # 检测金叉
            if last_row['golden_cross']:
                state['last_golden_cross'] = last_row['timestamp']
                state['trend_direction'] = 'bullish'
                state['pullback_observed'] = False
                msg = f"🔄 EMA20金叉MA170\n{base_msg}"
                logger.info(msg.replace("\n",", "))
                # 发送消息
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"\n{msg}")
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

            # 检测死叉
            if last_row['death_cross']:
                state['last_death_cross'] = last_row['timestamp']
                state['trend_direction'] = 'bearish'
                state['pullback_observed'] = False
                msg = f"🔄 EMA20死叉MA170\n{base_msg}"
                logger.info(msg.replace("\n",", "))
                # 发送消息
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"\n{msg}")
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

            # 检测回踩观察信号
            if state['trend_direction'] == 'bullish' and last_row['pullback_to_ma170'] and not state['pullback_observed']:
                state['pullback_observed'] = True
                msg = f"👀 金叉后回踩MA170观察信号\n{base_msg}"
                logger.info(msg.replace("\n",", "))
                # 发送消息
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"\n{msg}")
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

            if state['trend_direction'] == 'bearish' and last_row['pullback_to_ema20'] and not state['pullback_observed']:
                state['pullback_observed'] = True
                msg = f"👀 死叉后回踩EMA20观察信号\n{base_msg}"
                logger.info(msg.replace("\n",", "))
                # 发送消息
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"\n{msg}")
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

            # 检测买入信号
            if last_row['bullish_signal'] and state['trend_direction'] == 'bullish':
                reversal_type = ""
                if last_row['long_lower_shadow']:
                    reversal_type = "长下影线"
                elif last_row['bullish_engulfing']:
                    reversal_type = "看涨吞没"

                msg = f"🚀 买入信号 ({reversal_type})\n{base_msg}"
                logger.info(msg.replace("\n",", "))
                # 发送消息
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"\n{msg}")
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

            # 检测卖出信号
            if last_row['bearish_signal'] and state['trend_direction'] == 'bearish':
                reversal_type = ""
                if last_row['long_upper_shadow']:
                    reversal_type = "长上影线"
                elif last_row['bearish_engulfing']:
                    reversal_type = "看跌吞没"

                msg = f"📉 卖出信号 ({reversal_type})\n{base_msg}"
                logger.info(msg.replace("\n",", "))
                # 发送消息
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"\n{msg}")
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

        except (ExchangeNotAvailable, RequestTimeout, NetworkError) as e:
            logger.info(f"异常: {e}")
            exchange_is_down = True
            # 交易所服务宕机或不可用, 一会继续重试
            e_msg = f"❎ 交易所服务异常 ({type(e).__name__}), 暂无法 {sub_msg}, {EXCHANGE_DOWN_WAITING} 后重试..."
            logger.info(f"{current_file_name} {e_msg}")
            # 使用防重复发送工具
            await handle_exchange_error(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, e_msg)

            # 随机延迟重试，好像意义不大，因为每个任务复杂度不一样、执行时间也不一样，几乎不会出现同时重试
            wait_time = parse_interval(EXCHANGE_DOWN_WAITING)
            await asyncio.sleep(wait_time)
        except Exception as e:
            logger.error(f"检查过程中出错了:")
            raise e

        if not exchange_is_down:
            # 等待一段时间后再次检查
            try:
                current_timestamp = await exchange.fetch_time() # 获取服务器时间（毫秒时间戳）
            except (ExchangeNotAvailable, RequestTimeout, NetworkError) as e:
                logger.warning(f"获取服务器时间失败: {e}, 使用本地时间")
                # 如果获取服务器时间失败，使用本地时间（毫秒时间戳）
                current_timestamp = int(time.time() * 1000)
            except Exception as e:
                logger.error(f"获取服务器时间时发生未知错误: {e}, 使用本地时间")
                current_timestamp = int(time.time() * 1000)

            # 根据 interval 动态调整 sleep 时间
            try:
                wait_time = get_sleep_time(current_timestamp, interval)
                ftime = format_time(wait_time)
            except Exception as e:
                logger.error(f"计算等待时间时出错: {e}, 使用默认间隔")
                wait_time = parse_interval(interval)
                ftime = format_time(wait_time)
            logger.info(f"等待 {ftime} 后再次{sub_msg}...")
            await asyncio.sleep(wait_time)  # 每隔一段时间检查一次

            # time.sleep(sleep_time)  # 等待所需的时间

async def main():

    logger.info(f"{current_file_name} 开始！")
    # send_email(f"{current_file_name} 开始", "启动啦")
    # 发送Telegram消息
    try:
        await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"开始！")
    except Exception as e:
        logger.error(f"发送Telegram消息失败: {e}")

    try:

        # 只检查一组数据
        # await check_ema_ma_crossover_and_notify(exchange, trade_symbol, timeframe)

        # 从配置文件获取监控配置
        symbols = get_monitor_config('monitor.common.symbols', ["BTC/USDT:USDT"])
        intervals = get_monitor_config('monitor.common.intervals', ["5m", "15m"])
        ema_period = get_monitor_config('monitor.crossover.ema_period', 20)
        ma_period = get_monitor_config('monitor.crossover.ma_period', 170)
        pullback_tolerance = get_monitor_config('monitor.crossover.pullback_tolerance', 0.5)

        # 构建配置列表
        configs = []
        for symbol in symbols:
            for interval in intervals:
                configs.append({
                    "symbol": symbol,
                    "interval": interval
                })

        logger.info(f"📋 从配置加载: {len(symbols)}个币种, {len(intervals)}个时间间隔, EMA{ema_period}/MA{ma_period}, 回踩容忍度{pullback_tolerance}%")

        # 创建任务列表
        tasks = []
        for i,config in enumerate(configs):
            task = asyncio.create_task(
                check_ema_ma_crossover_and_notify(
                    exchange,
                    config["symbol"],
                    config["interval"]
                ),
                name=f"{current_file_name}_{i}"
            )
            tasks.append(task)
            await asyncio.sleep(0.5)  # 延迟 0.5 秒后启动下一个任务

        # 等待所有任务完成
        await asyncio.gather(*tasks)


    except CustomError as e:
        reason = f"{str(e)}"
    except asyncio.CancelledError:
        # 任务被取消，输出换行符避免与^C共行
        print_ctrl_c_newline()
        reason = f"主动取消"
    except Exception as e:
        logger.error(f"报错: {traceback.format_exc()}")
        reason = f"报错: {repr(e)}"
    finally:
        # 显示 `reason` 的默认值，以防止未设置导致的问题
        if 'reason' not in locals():  # 判断 reason 是否被赋值
            reason = "执行结束"
        # send_email(f"{current_file_name} 停止", f"{reason}")
        # 发送Telegram消息
        try:
            await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"停止！原因: {reason}")
        except Exception as e:
            logger.error(f"发送Telegram消息失败: {e}")
        # exchange由全局管理，无需在此处关闭
        logger.info(f"{current_file_name} 停止！原因: {reason}")
        return {"task_name":current_file_name, "task_result":reason}

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        # 任务被取消，输出换行符避免与^C共行
        print_ctrl_c_newline()
        logger.info(f"主动取消")
    finally:
        # 程序级清理（关闭exchange）- 只在独立运行时执行
        from utils.exchange_manager import close_global_exchange
        try:
            asyncio.run(close_global_exchange())
            logger.info("✅ Exchange连接已关闭")
        except Exception as e:
            logger.error(f"Exchange关闭时出错: {e}")
