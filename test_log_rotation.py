#!/usr/bin/env python3
"""测试日志轮转和文件命名"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.append('.')

from utils.utils_logger import setup_logger

def test_log_rotation():
    """测试日志轮转功能"""
    print("🧪 测试日志轮转和文件命名...")
    
    # 创建一个小的日志文件用于测试轮转
    test_logger = setup_logger("test_rotation", "test_rotation.log")
    
    # 模拟大量日志输出来触发轮转
    print("📝 生成大量日志以触发轮转...")
    
    # 写入大量数据来触发轮转（每个文件限制5MB）
    large_message = "这是一个测试消息，用于填充日志文件以触发轮转。" * 100
    
    for i in range(1000):  # 写入1000条消息
        test_logger.info(f"测试消息 {i}: {large_message}")
        
        # 每100条消息检查一次文件大小
        if i % 100 == 0:
            log_file = Path("logs/test_rotation.log")
            if log_file.exists():
                size_mb = log_file.stat().st_size / (1024 * 1024)
                print(f"📊 当前日志文件大小: {size_mb:.2f} MB")
                
                # 检查是否有轮转文件生成
                logs_dir = Path("logs")
                rotation_files = list(logs_dir.glob("test_rotation-*-*.log"))
                if rotation_files:
                    print(f"🔄 发现轮转文件: {len(rotation_files)} 个")
                    for file in rotation_files:
                        print(f"   📄 {file.name}")
                    break
    
    print("✅ 日志轮转测试完成")
    
    # 清理测试文件
    cleanup_test_files()

def cleanup_test_files():
    """清理测试生成的文件"""
    print("🧹 清理测试文件...")
    logs_dir = Path("logs")
    
    # 删除测试相关的日志文件
    test_files = list(logs_dir.glob("test_rotation*"))
    for file in test_files:
        try:
            file.unlink()
            print(f"🗑️  删除: {file.name}")
        except Exception as e:
            print(f"❌ 删除失败 {file.name}: {e}")

if __name__ == "__main__":
    test_log_rotation()
