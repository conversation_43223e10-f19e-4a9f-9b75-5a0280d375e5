from enum import Enum

# 自定义异常枚举
class ErrorCode(Enum):

    NETWORK_ERROR = (501, "网络错误，请稍后重试")

    INSUFFICIENT_AMOUNT = (700, "可用金额太少，请再入点金吧")
    NOT_SET_DUAL = (701, "单向持仓模式下有持仓或者有挂单，无法设置为双向持仓模式，请自行平仓或撤单")
    SET_DUAL_FAIL = (702, "设置为双向持仓模式时失败")
    OHLCV_NOT_ENOUGH = (703, "OHLCV数据不足, 请检查历史数据是否完整")

    API_LIMIT_EXCEEDED = (798, "API调用次数超限, 请稍后重试")
    UNKNOWN_ERROR = (799, "未知错误")

    def __init__(self, code, message):
        self.code = code
        self.message = message

# 自定义异常
class CustomError(Exception):
    """
    自定义异常类, 支持使用ErrorCode枚举
    """
    def __init__(self, error_code: ErrorCode, details: str = None):
        self.code = error_code.code
        self.message = error_code.message
        self.details = details
        super().__init__(self.__str__())

    def __str__(self):
        base_message = f"[错误码{self.code}] {self.message}"
        if self.details:
            return f"{base_message} - {self.details}"
        return base_message
