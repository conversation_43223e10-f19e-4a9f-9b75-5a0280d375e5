"""Telegram Bot管理器 - 统一管理Telegram Bot的生命周期"""

import asyncio
from telegram.ext import MessageHand<PERSON>, filters
from .utils_telegram import init_telegram_bot, reply_to_message
from .config_manager import get_app_config
from .utils_common import print_ctrl_c_newline


class TelegramBotManager:
    """Telegram Bot管理器"""

    def __init__(self, bot_token: str, logger=None):
        self.bot_token = bot_token
        # 使用传入的logger，如果没有则使用None（后续用print）
        self.logger = logger
        self.app = None
        self.is_running = False
        
    def log_info(self, message: str):
        """统一的日志输出"""
        log_func = self.logger.info if self.logger else print
        log_func(message)

    def log_warning(self, message: str):
        """统一的警告输出"""
        if self.logger:
            self.logger.warning(message)
        else:
            print(f"⚠️  {message}")

    def log_debug(self, message: str):
        """统一的调试输出"""
        if self.logger:
            self.logger.debug(message)
        else:
            print(f"🔍 {message}")
    
    async def start_bot(self):
        """启动Telegram Bot"""
        retry_count = 0
        max_retries = get_app_config('app.error_handling.max_retries', 3)
        
        while retry_count < max_retries:
            try:
                # 创建应用实例
                self.app = init_telegram_bot(self.bot_token)
                # 注册处理器 - 处理所有文本消息（包括命令）
                self.app.add_handler(MessageHandler(filters.TEXT, reply_to_message))

                self.log_info(f"🤖 启动Telegram机器人 (尝试 {retry_count + 1}/{max_retries})")
                
                # 初始化并启动轮询
                await self.app.initialize()
                await self.app.start()
                await self.app.updater.start_polling()

                # 重置重试计数器，因为成功启动了
                retry_count = 0
                self.is_running = True
                self.log_info("✅ Telegram机器人启动成功")

                # 保持运行直到被取消
                try:
                    await asyncio.Event().wait()  # 无限等待，直到任务被取消
                except asyncio.CancelledError:
                    # 任务被取消，输出换行符避免与^C共行
                    print_ctrl_c_newline()
                    self.log_info("Telegram机器人任务被取消")
                    raise  # 抛出异常，让外层处理

            except asyncio.CancelledError:
                self.log_info("Telegram机器人正常关闭")
                break
            except Exception as e:
                error_str = str(e)
                retry_count += 1
                
                # 检查是否是Telegram相关的冲突错误
                if any(keyword in error_str for keyword in [
                    "Conflict", "terminated by other getUpdates request", "only one bot instance"
                ]):
                    self.log_warning(f"⚠️ Telegram Bot冲突 (尝试 {retry_count}/{max_retries}): {e}")
                    if retry_count < max_retries:
                        wait_time = retry_count * 10  # 递增等待时间
                        self.log_info(f"🔄 等待 {wait_time} 秒后重试...")
                        await asyncio.sleep(wait_time)
                    else:
                        self.log_warning("❌ Telegram Bot冲突重试次数已达上限，停止重试")
                        break
                elif any(keyword in error_str for keyword in [
                    "Pool timeout", "TimedOut", "NetworkError"
                ]):
                    self.log_info(f"🌐 Telegram网络错误，这通常是正常的: {type(e).__name__}")
                    break
                else:
                    self.log_warning(f"❌ Telegram机器人异常 (尝试 {retry_count}/{max_retries}): {e}")
                    if retry_count < max_retries:
                        await asyncio.sleep(5)
                    else:
                        self.log_warning("❌ Telegram机器人重试次数已达上限")
                        break
            finally:
                # 清理资源
                await self._cleanup_bot()
        
        self.log_info("🤖 Telegram机器人任务结束")
    
    async def _cleanup_bot(self):
        """清理Telegram Bot资源"""
        if self.app:
            try:
                # 从配置获取超时时间
                telegram_timeout = get_app_config('app.cleanup.telegram_timeout', 5)
                
                # 分步骤清理，每步单独处理超时
                cleanup_steps = [
                    ("停止更新器", self.app.updater.stop),
                    ("停止应用", self.app.stop),
                    ("关闭应用", self.app.shutdown)
                ]
                
                for step_name, cleanup_func in cleanup_steps:
                    try:
                        await asyncio.wait_for(cleanup_func(), timeout=telegram_timeout)
                        self.log_debug(f"✅ Telegram {step_name}完成")
                    except asyncio.TimeoutError:
                        self.log_warning(f"⏰ Telegram {step_name}超时({telegram_timeout}秒)，跳过继续清理")
                    except Exception as e:
                        if str(e).strip():
                            self.log_warning(f"⚠️ Telegram {step_name}出现错误: {e}")
                        else:
                            self.log_debug(f"🔄 Telegram {step_name}完成（无异常信息）")
                            
            except Exception as e:
                self.log_debug(f"Telegram Bot清理过程中出现异常: {e}")
            finally:
                self.app = None
                self.is_running = False
    
    async def stop_bot(self):
        """停止Telegram Bot"""
        if self.is_running and self.app:
            await self._cleanup_bot()


# 全局Telegram Bot管理器实例
_global_bot_manager = None


def get_bot_manager(bot_token: str, logger=None) -> TelegramBotManager:
    """获取全局Telegram Bot管理器实例"""
    global _global_bot_manager
    if _global_bot_manager is None:
        _global_bot_manager = TelegramBotManager(bot_token, logger)
    return _global_bot_manager


async def run_telegram_bot(bot_token: str, logger=None):
    """运行Telegram Bot（便捷函数）"""
    bot_manager = get_bot_manager(bot_token, logger)
    await bot_manager.start_bot()


async def stop_telegram_bot(bot_token: str, logger=None):
    """停止Telegram Bot（便捷函数）"""
    bot_manager = get_bot_manager(bot_token, logger)
    await bot_manager.stop_bot()
