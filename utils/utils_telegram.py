import asyncio
import time
from collections import defaultdict
from telegram import Bot, InlineKeyboardMarkup, InlineKeyboardButton
from utils.constants import TASK_DIVIDER
from telegram import Update
from telegram.ext import <PERSON><PERSON><PERSON>er, MessageHandler, CallbackQueryHandler, filters, ContextTypes
from .config_manager import get_telegram_config
from .signal_handler import trigger_stop, trigger_restart

# 全局限流控制
_message_queue = asyncio.Queue()
_last_send_time = defaultdict(float)
_rate_limit_lock = asyncio.Lock()
_queue_processor_started = False
_program_start_time = None  # 记录程序启动时间，用于过滤历史消息
_pending_stop_confirmations = {}  # 存储等待确认的停止命令 {user_id: timestamp}
_pending_restart_confirmations = {}  # 存储等待确认的重启命令 {user_id: timestamp}



# 全局logger，用于reply_to_message函数
_global_logger = None

async def _process_message_queue(logger=None):
    """处理消息队列，确保不超过Telegram API限制"""
    # 使用传入的logger，如果没有则使用print
    log_error = logger.error if logger else print

    last_batch_info = {}  # 记录每个聊天的最后一批消息信息

    while True:
        try:
            # 从队列中获取消息
            message_data = await _message_queue.get()

            # Telegram API限制：每秒最多30条消息，每分钟最多20条消息给同一个聊天
            current_time = time.time()
            chat_key = f"{message_data['chat_id']}"

            # 检查是否需要等待（每个聊天每0.3秒最多1条消息）
            time_since_last = current_time - _last_send_time[chat_key]
            if time_since_last < 0.3:
                wait_time = 0.3 - time_since_last
                await asyncio.sleep(wait_time)

            # 发送消息
            try:
                bot = Bot(token=message_data['bot_token'])
                await bot.send_message(
                    chat_id=message_data['chat_id'],
                    text=message_data['text'],
                    disable_web_page_preview=True
                )
                _last_send_time[chat_key] = time.time()

                # 记录这个聊天的最后一条消息信息
                last_batch_info[chat_key] = {
                    'bot_token': message_data['bot_token'],
                    'chat_id': message_data['chat_id'],
                    'last_message_time': time.time()
                }

            except Exception as e:
                # 如果是限流错误，等待更长时间后重试
                if "Flood control exceeded" in str(e):
                    print(f"Telegram限流，等待30秒后重试...")
                    await asyncio.sleep(30)
                    # 重新放入队列
                    await _message_queue.put(message_data)
                    # 标记当前任务完成（因为我们重新放入了队列）
                    _message_queue.task_done()
                else:
                    print(f"发送Telegram消息失败: {e}")
                    # 标记任务完成
                    _message_queue.task_done()
            else:
                # 成功发送，标记任务完成
                _message_queue.task_done()

                # 检查队列是否为空，如果为空则发送分割线
                await asyncio.sleep(1)  # 短暂等待，确保没有新消息立即加入
                if _message_queue.empty() and chat_key in last_batch_info:
                    # 发送分割线
                    try:
                        await bot.send_message(
                            chat_id=last_batch_info[chat_key]['chat_id'],
                            text=TASK_DIVIDER,
                            disable_web_page_preview=True
                        )
                        # 更新发送时间
                        _last_send_time[chat_key] = time.time()
                    except Exception as e:
                        log_error(f"发送分割线失败: {e}")

        except Exception as e:
            log_error(f"处理消息队列时出错: {e}")
            await asyncio.sleep(1)

async def _ensure_queue_processor(logger=None):
    """确保消息队列处理器已启动"""
    global _queue_processor_started
    async with _rate_limit_lock:
        if not _queue_processor_started:
            asyncio.create_task(_process_message_queue(logger))
            _queue_processor_started = True

async def send_telegram_message(bot_token, chat_id, prefix, message, logger=None):
    """
    发送 Telegram 消息（带限流）
    :param bot_token: Bot 的 API Token
    :param chat_id: 接收消息的 Chat ID
    :param prefix: 消息前缀
    :param message: 要发送的消息内容
    :param logger: 可选的logger实例
    """
    await _ensure_queue_processor(logger)
    
    message_data = {
        'bot_token': bot_token,
        'chat_id': chat_id,
        'text': f"[{prefix}] {message}"
    }
    
    await _message_queue.put(message_data)

async def send_telegram_message_without_prefix(bot_token, chat_id, message, logger=None):
    """
    发送 Telegram 消息（带限流）
    :param bot_token: Bot 的 API Token
    :param chat_id: 接收消息的 Chat ID
    :param message: 要发送的消息内容
    :param logger: 可选的logger实例
    """
    await _ensure_queue_processor(logger)
    
    message_data = {
        'bot_token': bot_token,
        'chat_id': chat_id,
        'text': message
    }
    
    await _message_queue.put(message_data)



def set_program_start_time():
    """设置程序启动时间"""
    global _program_start_time
    _program_start_time = time.time()

def cleanup_expired_confirmations():
    """清理过期的确认请求（超过30秒）"""
    current_time = time.time()

    # 清理过期的停止确认
    expired_stop_users = [
        user_id for user_id, timestamp in _pending_stop_confirmations.items()
        if current_time - timestamp > 30
    ]
    for user_id in expired_stop_users:
        del _pending_stop_confirmations[user_id]

    # 清理过期的重启确认
    expired_restart_users = [
        user_id for user_id, timestamp in _pending_restart_confirmations.items()
        if current_time - timestamp > 30
    ]
    for user_id in expired_restart_users:
        del _pending_restart_confirmations[user_id]



def set_telegram_logger(logger):
    """设置Telegram回复消息使用的logger"""
    global _global_logger
    _global_logger = logger

async def handle_callback_query(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理内联键盘回调"""
    query = update.callback_query
    await query.answer()  # 确认收到回调

    # 使用全局logger，如果没有则使用print
    log_info = _global_logger.info if _global_logger else print

    user_id = query.from_user.id
    user_info = query.from_user.username or query.from_user.first_name
    callback_data = query.data

    if callback_data.startswith("confirm_stop_"):
        # 确认停止
        expected_user_id = int(callback_data.split("_")[-1])
        if user_id == expected_user_id and user_id in _pending_stop_confirmations:
            # 检查确认时间是否在30秒内
            current_time = time.time()
            if current_time - _pending_stop_confirmations[user_id] <= 30:
                # 执行停止
                await query.edit_message_text("🛑 停止指令已确认，量化程序即将关闭...")
                log_info(f"📱 通过按钮确认停止指令 - 发送者: {user_info}")

                # 清除待确认状态
                del _pending_stop_confirmations[user_id]

                # 触发停止
                trigger_stop("远程停止指令", _global_logger)
                log_info(f"📱 Telegram远程停止指令已触发 - 发送者: {user_info}")
            else:
                # 超时
                await query.edit_message_text("⏰ 确认超时，停止操作已取消")
                if user_id in _pending_stop_confirmations:
                    del _pending_stop_confirmations[user_id]
        else:
            await query.edit_message_text("❌ 无效的确认操作")

    elif callback_data.startswith("cancel_stop_"):
        # 取消停止
        expected_user_id = int(callback_data.split("_")[-1])
        if user_id == expected_user_id:
            await query.edit_message_text("✅ 停止操作已取消")
            if user_id in _pending_stop_confirmations:
                del _pending_stop_confirmations[user_id]
            log_info(f"📱 取消停止指令 - 发送者: {user_info}")
        else:
            await query.edit_message_text("❌ 无效的取消操作")

    elif callback_data.startswith("confirm_restart_"):
        # 确认重启
        expected_user_id = int(callback_data.split("_")[-1])
        if user_id == expected_user_id and user_id in _pending_restart_confirmations:
            # 检查确认时间是否在30秒内
            current_time = time.time()
            if current_time - _pending_restart_confirmations[user_id] <= 30:
                # 执行重启
                await query.edit_message_text("🔄 重启指令已确认，量化程序即将重启...")
                log_info(f"📱 通过按钮确认重启指令 - 发送者: {user_info}")

                # 清除待确认状态
                del _pending_restart_confirmations[user_id]

                # 触发重启
                trigger_restart("远程重启指令", _global_logger)
                log_info(f"📱 Telegram远程重启指令已触发 - 发送者: {user_info}")
            else:
                # 超时
                await query.edit_message_text("⏰ 确认超时，重启操作已取消")
                if user_id in _pending_restart_confirmations:
                    del _pending_restart_confirmations[user_id]
        else:
            await query.edit_message_text("❌ 无效的确认操作")

    elif callback_data.startswith("cancel_restart_"):
        # 取消重启
        expected_user_id = int(callback_data.split("_")[-1])
        if user_id == expected_user_id:
            await query.edit_message_text("✅ 重启操作已取消")
            if user_id in _pending_restart_confirmations:
                del _pending_restart_confirmations[user_id]
            log_info(f"📱 取消重启指令 - 发送者: {user_info}")
        else:
            await query.edit_message_text("❌ 无效的取消操作")

async def reply_to_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """回复消息，只回复程序运行期间的消息，并处理停止命令"""
    # 使用全局logger，如果没有则使用print
    log_info = _global_logger.info if _global_logger else print
    log_warning = _global_logger.warning if _global_logger else print

    if update.message and update.message.chat.type in ["group", "supergroup"]:
        # 清理过期的确认请求
        cleanup_expired_confirmations()

        # 检查消息时间是否在程序启动之后
        message_time = update.message.date.timestamp()
        if _program_start_time is None or message_time >= _program_start_time:
            message_text = update.message.text.strip()
            # 处理群组中的@bot命令，移除@bot_name部分
            if '@' in message_text and message_text.startswith('/'):
                message_text = message_text.split('@')[0]
            user_info = update.message.from_user.username or update.message.from_user.first_name

            log_info(f"📱 收到消息: '{update.message.text.strip()}' -> 处理为: '{message_text}' - 发送者: {user_info}")

            # 检查是否是停止命令
            stop_commands = get_telegram_config('telegram.commands.stop', [])
            if message_text.lower() in [cmd.lower() for cmd in stop_commands]:
                user_id = update.message.from_user.id
                current_time = time.time()

                # 检查是否已经有待确认的停止命令
                if user_id in _pending_stop_confirmations:
                    # 检查确认时间是否在30秒内
                    if current_time - _pending_stop_confirmations[user_id] <= 30:
                        # 执行停止
                        stop_reply = get_telegram_config('telegram.messages.stop_reply', '🛑 确认停止指令，量化程序即将关闭...')
                        await update.message.reply_text(stop_reply)
                        log_info(f"📱 确认停止指令: '{message_text}' - 发送者: {user_info}")

                        # 清除待确认状态
                        del _pending_stop_confirmations[user_id]

                        # 使用新的信号处理器触发停止
                        trigger_stop("远程停止指令", _global_logger)
                        log_info(f"📱 Telegram远程停止指令已触发 - 发送者: {user_info}")
                        return
                    else:
                        # 超时，清除旧的确认状态
                        del _pending_stop_confirmations[user_id]

                # 第一次停止命令，要求确认
                _pending_stop_confirmations[user_id] = current_time

                # 创建确认按钮
                keyboard = InlineKeyboardMarkup([
                    [
                        InlineKeyboardButton("✅ 确认停止", callback_data=f"confirm_stop_{user_id}"),
                        InlineKeyboardButton("❌ 取消", callback_data=f"cancel_stop_{user_id}")
                    ]
                ])

                confirm_message = (
                    "⚠️ **危险操作确认**\n\n"
                    "您即将停止量化程序，这将：\n"
                    "• 停止所有监控和交易逻辑\n"
                    "• 关闭所有连接\n"
                    "• 程序完全退出\n\n"
                    "请在30秒内确认您的操作："
                )

                await update.message.reply_text(
                    confirm_message,
                    reply_markup=keyboard,
                    parse_mode='Markdown'
                )
                log_info(f"📱 收到停止指令，等待确认: '{message_text}' - 发送者: {user_info}")
                return

            # 检查是否是状态查询命令
            status_commands = get_telegram_config('telegram.commands.status', [])
            if message_text.lower() in [cmd.lower() for cmd in status_commands]:
                status_reply = get_telegram_config('telegram.messages.status_reply', '✅ 量化程序运行正常，所有任务正在执行中')
                await update.message.reply_text(status_reply)
                log_info(f"📱 收到状态查询 - 发送者: {user_info}")
                return

            # 检查是否是重启命令
            restart_commands = get_telegram_config('telegram.commands.restart', [])
            if message_text.lower() in [cmd.lower() for cmd in restart_commands]:
                user_id = update.message.from_user.id
                current_time = time.time()

                # 检查是否已经有待确认的重启命令
                if user_id in _pending_restart_confirmations:
                    # 检查确认时间是否在30秒内
                    if current_time - _pending_restart_confirmations[user_id] <= 30:
                        # 执行重启
                        restart_reply = get_telegram_config('telegram.messages.restart_reply', '🔄 确认重启指令，量化程序即将重启...')
                        await update.message.reply_text(restart_reply)
                        log_info(f"📱 确认重启指令: '{message_text}' - 发送者: {user_info}")

                        # 清除待确认状态
                        del _pending_restart_confirmations[user_id]

                        # 使用新的信号处理器触发重启
                        trigger_restart("远程重启指令", _global_logger)
                        log_info(f"📱 Telegram远程重启指令已触发 - 发送者: {user_info}")
                        return
                    else:
                        # 超时，清除旧的确认状态
                        del _pending_restart_confirmations[user_id]

                # 第一次重启命令，要求确认
                _pending_restart_confirmations[user_id] = current_time

                # 创建确认按钮
                keyboard = InlineKeyboardMarkup([
                    [
                        InlineKeyboardButton("✅ 确认重启", callback_data=f"confirm_restart_{user_id}"),
                        InlineKeyboardButton("❌ 取消", callback_data=f"cancel_restart_{user_id}")
                    ]
                ])

                confirm_message = (
                    "⚠️ **重启操作确认**\n\n"
                    "您即将重启量化程序，这将：\n"
                    "• 停止所有当前任务\n"
                    "• 重新加载所有配置\n"
                    "• 重新启动所有监控和交易逻辑\n\n"
                    "请在30秒内确认您的操作："
                )

                await update.message.reply_text(
                    confirm_message,
                    reply_markup=keyboard,
                    parse_mode='Markdown'
                )
                log_info(f"📱 收到重启指令，等待确认: '{message_text}' - 发送者: {user_info}")
                return

            # 检查是否是重载配置命令
            reload_commands = get_telegram_config('telegram.commands.reload', [])
            if message_text.lower() in [cmd.lower() for cmd in reload_commands]:
                reload_reply = get_telegram_config('telegram.messages.reload_reply', '🔄 收到重载指令，正在重新加载所有配置...')
                await update.message.reply_text(reload_reply)
                log_info(f"📱 收到重载配置指令: '{message_text}' - 发送者: {user_info}")

                # 重载所有配置
                from utils.config_manager import config_manager
                config_manager.reload_all_configs()

                success_reply = get_telegram_config('telegram.messages.reload_success_reply', '✅ 配置重载完成')
                await update.message.reply_text(success_reply)
                log_info(f"📱 配置重载完成 - 发送者: {user_info}")
                return

            # 检查是否是重载监控配置命令
            reload_monitor_commands = get_telegram_config('telegram.commands.reload_monitor', [])
            if message_text.lower() in [cmd.lower() for cmd in reload_monitor_commands]:
                reload_reply = get_telegram_config('telegram.messages.reload_monitor_reply', '🔄 收到重载指令，正在重新加载监控配置...')
                await update.message.reply_text(reload_reply)
                log_info(f"📱 收到重载监控配置指令: '{message_text}' - 发送者: {user_info}")

                # 重载监控配置
                from utils.config_manager import config_manager
                config_manager.reload_config('monitor')

                success_reply = get_telegram_config('telegram.messages.reload_success_reply', '✅ 配置重载完成')
                await update.message.reply_text(success_reply)
                log_info(f"📱 监控配置重载完成 - 发送者: {user_info}")
                return

            # 检查是否是重载应用配置命令
            reload_app_commands = get_telegram_config('telegram.commands.reload_app', [])
            if message_text.lower() in [cmd.lower() for cmd in reload_app_commands]:
                reload_reply = get_telegram_config('telegram.messages.reload_app_reply', '🔄 收到重载指令，正在重新加载应用配置...')
                await update.message.reply_text(reload_reply)
                log_info(f"📱 收到重载应用配置指令: '{message_text}' - 发送者: {user_info}")

                # 重载应用配置
                from utils.config_manager import config_manager
                config_manager.reload_config('app')

                success_reply = get_telegram_config('telegram.messages.reload_success_reply', '✅ 配置重载完成')
                await update.message.reply_text(success_reply)
                log_info(f"📱 应用配置重载完成 - 发送者: {user_info}")
                return

            # 检查是否是重载交易配置命令
            reload_trader_commands = get_telegram_config('telegram.commands.reload_trader', [])
            if message_text.lower() in [cmd.lower() for cmd in reload_trader_commands]:
                reload_reply = get_telegram_config('telegram.messages.reload_trader_reply', '🔄 收到重载指令，正在重新加载交易配置...')
                await update.message.reply_text(reload_reply)
                log_info(f"📱 收到重载交易配置指令: '{message_text}' - 发送者: {user_info}")

                # 重载交易配置
                from utils.config_manager import config_manager
                config_manager.reload_config('trader')

                success_reply = get_telegram_config('telegram.messages.reload_success_reply', '✅ 配置重载完成')
                await update.message.reply_text(success_reply)
                log_info(f"📱 交易配置重载完成 - 发送者: {user_info}")
                return

            # 检查是否是重载交易所配置命令
            reload_exchange_commands = get_telegram_config('telegram.commands.reload_exchange', [])
            if message_text.lower() in [cmd.lower() for cmd in reload_exchange_commands]:
                reload_reply = get_telegram_config('telegram.messages.reload_exchange_reply', '🔄 收到重载指令，正在重新加载交易所配置...')
                await update.message.reply_text(reload_reply)
                log_info(f"📱 收到重载交易所配置指令: '{message_text}' - 发送者: {user_info}")

                # 重载交易所配置
                from utils.config_manager import config_manager
                config_manager.reload_config('exchange')

                success_reply = get_telegram_config('telegram.messages.reload_success_reply', '✅ 配置重载完成')
                await update.message.reply_text(success_reply)
                log_info(f"📱 交易所配置重载完成 - 发送者: {user_info}")
                return

            # 其他消息不回复，只记录日志
            log_info(f"📱 收到未识别消息: '{message_text}' - 发送者: {user_info}")
        # 如果是历史消息，不回复

def init_telegram_bot(bot_token: str):
    """初始化机器人（不启动轮询）"""
    app = ApplicationBuilder().token(bot_token).pool_timeout(10).build()

    # 添加回调查询处理器
    app.add_handler(CallbackQueryHandler(handle_callback_query))

    return app

async def wait_for_message_queue(logger=None):
    """等待消息队列中的所有消息都被发送完毕

    Args:
        logger: 日志记录器，如果提供则使用logger记录，否则使用print
    """
    # 使用传入的logger，如果没有则使用print
    log_info = logger.info if logger else print
    log_warning = logger.warning if logger else print

    queue_size = _message_queue.qsize()
    if queue_size > 0:
        # 动态计算超时时间：基础时间 + 每条消息的预估时间
        base_timeout = 10  # 基础等待时间（秒）
        per_message_timeout = 2  # 每条消息预估处理时间（秒）
        max_timeout = 300  # 最大超时时间（5分钟）

        dynamic_timeout = min(base_timeout + (queue_size * per_message_timeout), max_timeout)

        log_info(f"📤 等待消息队列清空，当前队列大小: {queue_size}，动态超时: {dynamic_timeout}秒")

        try:
            await asyncio.wait_for(_message_queue.join(), timeout=dynamic_timeout)
            log_info("✅ 消息队列已清空")
        except asyncio.TimeoutError:
            remaining_size = _message_queue.qsize()
            sent_count = queue_size - remaining_size
            log_warning(f"⚠️ 消息队列清空超时，已发送: {sent_count}/{queue_size}，剩余: {remaining_size}条")
            log_info("💡 建议：如果经常超时，可以增加per_message_timeout值")
    else:
        log_info("📭 消息队列已为空，无需等待")
