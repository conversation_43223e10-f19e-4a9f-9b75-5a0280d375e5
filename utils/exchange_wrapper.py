"""Exchange包装器 - 为现有代码提供无缝的全局exchange访问

⚠️ 安全提示：
- 禁止调用 close() 方法
- 禁止调用 set_* 配置方法
- Exchange由全局管理器统一管理
- 如需特殊功能请联系系统管理员
"""

from typing import Optional, Dict
from .exchange_manager import get_global_exchange, record_api_call

class ExchangeWrapper:
    """Exchange包装器，将API调用转发到全局exchange实例"""

    # 禁止调用的危险方法列表
    _FORBIDDEN_METHODS = {
        'close',           # 禁止关闭exchange
        'set_sandbox_mode', # 禁止修改沙盒模式
        'setSandboxMode',  # 驼峰命名别名
        'set_proxy',       # 禁止修改代理设置
        'setProxy',        # 驼峰命名别名
        'set_headers',     # 禁止修改请求头
        'setHeaders',      # 驼峰命名别名
        'set_timeout',     # 禁止修改超时设置
        'setTimeout',      # 驼峰命名别名
        'set_api_key',     # 禁止修改API密钥
        'setApiKey',       # 驼峰命名别名
        'set_secret',      # 禁止修改API密钥
        'setSecret',       # 驼峰命名别名
        'set_password',    # 禁止修改密码
        'setPassword',     # 驼峰命名别名
        'set_uid',         # 禁止修改UID
        'setUid',          # 驼峰命名别名
    }

    # 需要特殊处理的方法（不禁止但有特殊逻辑）
    _SPECIAL_METHODS = {
        'load_markets',    # 已经预加载，直接返回
        'loadMarkets',     # load_markets的驼峰命名别名
    }

    def __init__(self, caller_name: str, logger=None):
        self.caller_name = caller_name
        self.logger = logger
        self._global_exchange = None
    
    async def _get_exchange(self):
        """获取全局exchange实例"""
        if self._global_exchange is None:
            self._global_exchange = await get_global_exchange(self.logger)
        return self._global_exchange
    
    async def _call_with_logging(self, method_name: str, *args, **kwargs):
        """调用API并记录"""
        exchange = await self._get_exchange()
        record_api_call(self.caller_name, method_name)

        method = getattr(exchange, method_name)
        return await method(*args, **kwargs)
    
    # 常用API方法的包装
    async def fetch_ohlcv(self, symbol: str, timeframe: str = '1m', since: Optional[int] = None, limit: Optional[int] = None, params: Dict = {}):
        """获取OHLCV数据"""
        return await self._call_with_logging('fetch_ohlcv', symbol, timeframe, since, limit, params)
    
    async def fetch_order_book(self, symbol: str, limit: Optional[int] = None, params: Dict = {}):
        """获取订单簿数据"""
        return await self._call_with_logging('fetch_order_book', symbol, limit, params)
    
    async def fetch_open_interest(self, symbol: str, params: Dict = {}):
        """获取持仓量数据"""
        return await self._call_with_logging('fetch_open_interest', symbol, params)
    
    async def fetch_time(self, params: Dict = {}):
        """获取服务器时间"""
        return await self._call_with_logging('fetch_time', params)
    
    async def load_markets(self, reload: bool = False, params: Dict = {}):
        """加载市场信息（实际上全局exchange已经预加载了）"""
        exchange = await self._get_exchange()
        return exchange.markets  # 直接返回已加载的markets
    
    async def fetch_tickers(self, symbols: Optional[list] = None, params: Dict = {}):
        """获取ticker数据"""
        return await self._call_with_logging('fetch_tickers', symbols, params)
    
    async def fetch_ticker(self, symbol: str, params: Dict = {}):
        """获取单个ticker数据"""
        return await self._call_with_logging('fetch_ticker', symbol, params)
    
    async def fetch_trades(self, symbol: str, since: Optional[int] = None, limit: Optional[int] = None, params: Dict = {}):
        """获取交易数据"""
        return await self._call_with_logging('fetch_trades', symbol, since, limit, params)
    
    async def fetch_balance(self, params: Dict = {}):
        """获取账户余额"""
        return await self._call_with_logging('fetch_balance', params)
    
    async def fetch_positions(self, symbols: Optional[list] = None, params: Dict = {}):
        """获取持仓信息"""
        return await self._call_with_logging('fetch_positions', symbols, params)
    
    async def fetch_open_orders(self, symbol: Optional[str] = None, since: Optional[int] = None, limit: Optional[int] = None, params: Dict = {}):
        """获取未成交订单"""
        return await self._call_with_logging('fetch_open_orders', symbol, since, limit, params)
    
    async def create_order(self, symbol: str, type: str, side: str, amount: float, price: Optional[float] = None, params: Dict = {}):
        """创建订单"""
        return await self._call_with_logging('create_order', symbol, type, side, amount, price, params)
    
    async def cancel_order(self, id: str, symbol: Optional[str] = None, params: Dict = {}):
        """取消订单"""
        return await self._call_with_logging('cancel_order', id, symbol, params)

    async def close(self):
        """禁止关闭exchange - 提供友好的错误信息"""
        raise PermissionError(
            f"🚫 禁止调用 'close()' 方法！\n"
            f"原因：Exchange由全局管理器统一管理，不允许单独关闭。\n"
            f"调用者：{self.caller_name}\n"
            f"说明：程序结束时会自动关闭所有资源，无需手动调用。"
        )


    
    # 属性访问
    @property
    async def markets(self):
        """获取markets信息"""
        exchange = await self._get_exchange()
        return exchange.markets
    
    def __getattr__(self, name):
        """动态属性访问，用于访问exchange的其他属性和方法"""

        # 安全检查：禁止调用危险方法
        if name in self._FORBIDDEN_METHODS:
            def forbidden_method(*args, **kwargs):
                raise PermissionError(
                    f"🚫 禁止调用 '{name}' 方法！\n"
                    f"原因：此方法可能影响全局exchange实例的稳定性。\n"
                    f"调用者：{self.caller_name}\n"
                    f"如需此功能，请联系系统管理员。"
                )
            return forbidden_method

        # 特殊方法处理
        if name in self._SPECIAL_METHODS:
            if name in ('load_markets', 'loadMarkets'):
                # load_markets/loadMarkets已经在全局exchange中预加载，直接返回结果
                async def load_markets_wrapper(*args, **kwargs):
                    exchange = await self._get_exchange()
                    return exchange.markets
                return load_markets_wrapper

        # 正常方法调用
        async def wrapper(*args, **kwargs):
            exchange = await self._get_exchange()
            attr = getattr(exchange, name)
            if callable(attr):
                record_api_call(self.caller_name, name)
                return await attr(*args, **kwargs)
            else:
                return attr
        return wrapper

def create_exchange_wrapper(caller_name: str, logger=None) -> ExchangeWrapper:
    """创建exchange包装器的便捷函数"""
    return ExchangeWrapper(caller_name, logger)
