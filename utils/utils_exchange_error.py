"""交易所异常处理工具，防止重复发送消息"""
import time
from utils.utils_telegram import send_telegram_message

# 按文件名记录上次发送异常消息的时间
_last_exchange_error_time = {}

async def handle_exchange_error(bot_token, chat_id, file_name, error_msg, min_interval=300):
    """
    处理交易所异常消息，防止重复发送（按文件分别限制）

    Args:
        bot_token: Telegram bot token
        chat_id: Telegram chat ID
        file_name: 当前文件名
        error_msg: 错误消息
        min_interval: 最小发送间隔（秒），默认5分钟

    Returns:
        bool: 是否发送了消息
    """
    global _last_exchange_error_time

    current_time = time.time()
    last_time = _last_exchange_error_time.get(file_name, None)
    should_send = (last_time is None or current_time - last_time > min_interval)

    if should_send:
        try:
            await send_telegram_message(bot_token, chat_id, file_name, error_msg)
            _last_exchange_error_time[file_name] = current_time
            return True
        except Exception as e:
            print(f"发送Telegram消息失败: {e}")
            return False
    else:
        # print(f"跳过重复的交易所错误消息发送（{file_name}距离上次发送不足{min_interval//60}分钟）")
        return False
