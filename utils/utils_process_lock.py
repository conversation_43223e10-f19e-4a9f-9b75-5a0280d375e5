"""进程锁定工具，防止多个实例同时运行"""
import os
import sys
import fcntl
import atexit
from pathlib import Path

class ProcessLock:
    def __init__(self, lock_file_path):
        self.lock_file_path = Path(lock_file_path)
        self.lock_file = None
        
    def acquire(self):
        """获取进程锁"""
        try:
            # 确保锁文件目录存在
            self.lock_file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 打开锁文件
            self.lock_file = open(self.lock_file_path, 'w')
            
            # 尝试获取独占锁（非阻塞）
            fcntl.flock(self.lock_file.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
            
            # 写入当前进程ID
            self.lock_file.write(str(os.getpid()))
            self.lock_file.flush()
            
            # 注册退出时释放锁
            atexit.register(self.release)
            
            return True
            
        except (IOError, OSError) as e:
            if self.lock_file:
                self.lock_file.close()
                self.lock_file = None
            return False
    
    def release(self):
        """释放进程锁"""
        if self.lock_file:
            try:
                fcntl.flock(self.lock_file.fileno(), fcntl.LOCK_UN)
                self.lock_file.close()
                # 删除锁文件
                if self.lock_file_path.exists():
                    self.lock_file_path.unlink()
            except:
                pass
            finally:
                self.lock_file = None
    
    def is_locked(self):
        """检查是否已被锁定"""
        if not self.lock_file_path.exists():
            return False
            
        try:
            with open(self.lock_file_path, 'r') as f:
                pid = int(f.read().strip())
                
            # 检查进程是否还在运行
            try:
                os.kill(pid, 0)  # 发送信号0检查进程是否存在
                return True
            except OSError:
                # 进程不存在，删除过期的锁文件
                self.lock_file_path.unlink()
                return False
                
        except (ValueError, FileNotFoundError):
            return False

def ensure_single_instance(lock_file_path, program_name="程序", wait_for_release=False, max_wait_seconds=10):
    """确保只有一个程序实例在运行

    Args:
        lock_file_path: 锁文件路径
        program_name: 程序名称
        wait_for_release: 是否等待锁释放（用于重启场景）
        max_wait_seconds: 最大等待时间
    """
    import time

    lock = ProcessLock(lock_file_path)

    if wait_for_release and lock.is_locked():
        print(f"检测到{program_name}正在运行，等待其退出...")
        start_time = time.time()

        while lock.is_locked() and (time.time() - start_time) < max_wait_seconds:
            time.sleep(0.5)

        if lock.is_locked():
            print(f"错误: 等待{max_wait_seconds}秒后，{program_name}仍在运行中。")
            sys.exit(1)
        else:
            print(f"✅ 前一个{program_name}实例已退出")

    elif lock.is_locked():
        print(f"错误: {program_name}已经在运行中，请先停止现有实例。")
        sys.exit(1)

    if not lock.acquire():
        print(f"错误: 无法获取进程锁，{program_name}可能已经在运行。")
        sys.exit(1)

    # print(f"{program_name}进程锁获取成功，确保单实例运行。")
    return lock