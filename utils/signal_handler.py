"""信号处理模块 - 处理程序停止信号和生命周期管理"""

import asyncio
import signal
import sys
from typing import Optional, Callable, Dict, Any
from .utils_common import print_ctrl_c_newline


# 信号描述映射 - 技术准确性 + 用户友好性
SIGNAL_DESCRIPTIONS = {
    2: "用户中断(Ctrl+C)",
    3: "用户退出(Ctrl+\\)",
    9: "强制终止",
    15: "程序终止请求",
    1: "终端关闭",
}

# 停止原因的用户友好描述
STOP_REASON_DESCRIPTIONS = {
    "远程停止指令": "📱 Telegram远程停止",
    "程序异常": "❌ 程序异常退出",
    "所有任务完成": "✅ 所有任务正常完成",
    "主动取消": "🔄 程序主动取消",
}


class SignalHandler:
    """信号处理器 - 统一管理程序停止信号"""
    
    def __init__(self, logger=None):
        self.logger = logger
        self.stop_event = asyncio.Event()
        self.restart_event = asyncio.Event()
        self.shutdown_callbacks = []
        self.is_shutting_down = False
        self.is_restarting = False
        self.stop_reason = None  # 记录停止原因
        
    def log_info(self, message: str):
        """统一的日志输出"""
        if self.logger:
            self.logger.info(message)
        else:
            print(message)
            
    def log_warning(self, message: str):
        """统一的警告输出"""
        if self.logger:
            self.logger.warning(message)
        else:
            print(f"WARNING: {message}")
    
    def add_shutdown_callback(self, callback: Callable):
        """添加关闭回调函数"""
        self.shutdown_callbacks.append(callback)
    
    def setup_signal_handlers(self):
        """设置系统信号处理器（Ctrl+C等）"""
        def signal_handler(signum, _):
            if not self.is_shutting_down:
                self.is_shutting_down = True
                # 获取用户友好的描述
                signal_desc = SIGNAL_DESCRIPTIONS.get(signum, f"未知信号{signum}")
                self.stop_reason = signal_desc
                print_ctrl_c_newline()
                self.log_info(f"🛑 收到停止信号: {signal_desc} (信号{signum})，开始优雅退出...")
                self.stop_event.set()
        
        # 设置信号处理器
        signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
        signal.signal(signal.SIGTERM, signal_handler)  # 终止信号
        
        if sys.platform != 'win32':
            signal.signal(signal.SIGHUP, signal_handler)   # 挂起信号
    
    async def wait_for_stop_signal(self) -> Dict[str, Any]:
        """等待停止信号事件"""
        try:
            # 等待停止事件被触发
            await self.stop_event.wait()

            # 根据记录的停止原因返回相应信息
            if self.stop_reason:
                # 获取用户友好的描述
                friendly_desc = STOP_REASON_DESCRIPTIONS.get(self.stop_reason, self.stop_reason)
                self.log_info(f"🛑 检测到停止信号: {friendly_desc}")
                return {"task_name": "stop_signal_checker", "task_result": self.stop_reason}
            else:
                # 兜底情况
                self.log_info("🛑 收到未知停止信号")
                return {"task_name": "stop_signal_checker", "task_result": "未知停止信号"}

        except asyncio.CancelledError:
            # 任务被取消，输出换行符避免与^C共行
            print_ctrl_c_newline()
            self.log_info("停止信号等待任务被取消")
            return {"task_name": "stop_signal_checker", "task_result": "主动取消"}
    
    def trigger_stop(self, reason: str = "远程停止"):
        """触发停止信号（用于Telegram远程停止等）"""
        if not self.is_shutting_down:
            self.is_shutting_down = True
            self.stop_reason = reason
            self.log_info(f"🛑 触发停止信号: {reason}")
            self.stop_event.set()

    def trigger_restart(self, reason: str = "远程重启"):
        """触发重启信号（用于Telegram远程重启等）"""
        if not self.is_shutting_down and not self.is_restarting:
            self.is_restarting = True
            self.is_shutting_down = True  # 重启也需要先停止
            self.stop_reason = reason
            self.log_info(f"🔄 触发重启信号: {reason}")
            self.restart_event.set()
            self.stop_event.set()  # 同时触发停止事件
    
    async def graceful_shutdown(self):
        """优雅关闭 - 执行所有关闭回调"""
        self.log_info("🔄 开始执行优雅关闭流程...")
        
        for i, callback in enumerate(self.shutdown_callbacks):
            try:
                self.log_info(f"🔄 执行关闭回调 {i+1}/{len(self.shutdown_callbacks)}")
                if asyncio.iscoroutinefunction(callback):
                    await callback()
                else:
                    callback()
            except Exception as e:
                self.log_warning(f"关闭回调执行失败: {e}")
        
        self.log_info("✅ 优雅关闭流程完成")


# 全局信号处理器实例
_global_signal_handler: Optional[SignalHandler] = None


def get_signal_handler(logger=None) -> SignalHandler:
    """获取全局信号处理器实例"""
    global _global_signal_handler
    if _global_signal_handler is None:
        _global_signal_handler = SignalHandler(logger)
    return _global_signal_handler


def setup_signal_handlers(logger=None):
    """设置信号处理器（便捷函数）"""
    handler = get_signal_handler(logger)
    handler.setup_signal_handlers()
    return handler


async def wait_for_stop_signal(logger=None) -> Dict[str, Any]:
    """等待停止信号（便捷函数）"""
    handler = get_signal_handler(logger)
    return await handler.wait_for_stop_signal()


def trigger_stop(reason: str = "远程停止", logger=None):
    """触发停止信号（便捷函数）"""
    handler = get_signal_handler(logger)
    handler.trigger_stop(reason)


def trigger_restart(reason: str = "远程重启", logger=None):
    """触发重启信号（便捷函数）"""
    handler = get_signal_handler(logger)
    handler.trigger_restart(reason)


def add_shutdown_callback(callback: Callable, logger=None):
    """添加关闭回调（便捷函数）"""
    handler = get_signal_handler(logger)
    handler.add_shutdown_callback(callback)
