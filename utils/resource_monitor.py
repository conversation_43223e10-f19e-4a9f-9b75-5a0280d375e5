"""资源监控工具 - 监控exchange实例的资源使用情况"""

import psutil
import time
import asyncio
import logging
from datetime import datetime
from typing import Dict, List
from .config_manager import get_app_config


class ResourceMonitor:
    """资源监控器，用于监控exchange实例的资源使用"""
    
    def __init__(self):
        self.start_time = time.time()
        self.initial_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        self.peak_memory = self.initial_memory  # 记录最高内存使用量
        self.api_call_count = 0
        self.api_call_history = []
        self.caller_stats = {}  # 存储每个调用者的统计信息
    
    def register_caller(self, caller_name: str):
        """注册一个API调用者（monitor/trader）"""
        if caller_name not in self.caller_stats:
            self.caller_stats[caller_name] = {
                'name': caller_name,
                'registered_at': datetime.now(),
                'api_calls': 0,
                'last_call_at': None
            }
    
    def record_api_call(self, caller_name: str, api_method: str):
        """记录API调用"""
        self.api_call_count += 1
        current_time = datetime.now()

        # 记录调用历史
        self.api_call_history.append({
            'timestamp': current_time,
            'caller': caller_name,
            'method': api_method
        })

        # 自动注册调用者（如果未注册）
        if caller_name not in self.caller_stats:
            self.register_caller(caller_name)

        # 更新调用者统计
        self.caller_stats[caller_name]['api_calls'] += 1
        self.caller_stats[caller_name]['last_call_at'] = current_time

        # 只保留最近1000条记录
        if len(self.api_call_history) > 1000:
            self.api_call_history = self.api_call_history[-1000:]

    def get_basic_memory_info(self) -> Dict:
        """获取基础内存信息（轻量级，用于频繁检查）"""
        current_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB

        # 更新峰值内存记录
        if current_memory > self.peak_memory:
            self.peak_memory = current_memory

        return {
            'current_memory_mb': round(current_memory, 2),
            'peak_memory_mb': round(self.peak_memory, 2),
            'initial_memory_mb': round(self.initial_memory, 2)
        }

    def get_memory_usage(self) -> Dict:
        """获取完整的内存使用情况（包含系统信息）"""
        # 获取基础内存信息（轻量级）
        basic_info = self.get_basic_memory_info()
        current_memory = basic_info['current_memory_mb']

        # 获取系统内存信息
        system_memory = psutil.virtual_memory()
        total_system_memory = system_memory.total / 1024 / 1024 / 1024  # GB
        used_system_memory = system_memory.used / 1024 / 1024 / 1024  # GB
        available_system_memory = system_memory.available / 1024 / 1024 / 1024  # GB

        # 计算不可用内存（缓存、缓冲区等）
        unavailable_system_memory = total_system_memory - available_system_memory

        # 使用psutil的percent，它更准确地反映了系统内存使用情况
        system_memory_percent = system_memory.percent

        return {
            'initial_memory_mb': basic_info['initial_memory_mb'],
            'current_memory_mb': basic_info['current_memory_mb'],
            'peak_memory_mb': basic_info['peak_memory_mb'],
            'memory_increase_mb': round(current_memory - self.initial_memory, 2),
            'memory_increase_percent': round((current_memory - self.initial_memory) / self.initial_memory * 100, 2),
            'peak_increase_mb': round(self.peak_memory - self.initial_memory, 2),
            'peak_increase_percent': round((self.peak_memory - self.initial_memory) / self.initial_memory * 100, 2),
            'system_total_gb': round(total_system_memory, 2),
            'system_used_gb': round(used_system_memory, 2),
            'system_unavailable_gb': round(unavailable_system_memory, 2),
            'system_available_gb': round(available_system_memory, 2),
            'system_usage_percent': round(system_memory_percent, 2),
            'initial_vs_system_percent': round((self.initial_memory / 1024) / total_system_memory * 100, 4),
            'current_vs_system_percent': round((current_memory / 1024) / total_system_memory * 100, 4),
            'peak_vs_system_percent': round((self.peak_memory / 1024) / total_system_memory * 100, 4)
        }
    
    def get_api_stats(self) -> Dict:
        """获取API调用统计"""
        runtime_hours = (time.time() - self.start_time) / 3600
        calls_per_hour = self.api_call_count / runtime_hours if runtime_hours > 0 else 0

        # 最近1分钟的调用次数
        one_minute_ago = datetime.now().timestamp() - 60
        recent_calls = len([call for call in self.api_call_history
                           if call['timestamp'].timestamp() > one_minute_ago])

        return {
            'total_api_calls': self.api_call_count,
            'runtime_hours': round(runtime_hours, 2),
            'calls_per_hour': round(calls_per_hour, 2),
            'calls_last_minute': recent_calls,
            'caller_count': len(self.caller_stats)
        }
    
    def get_caller_stats(self) -> List[Dict]:
        """获取各调用者的统计"""
        return [
            {
                'name': stats['name'],
                'registered_at': stats['registered_at'].strftime('%Y-%m-%d %H:%M:%S'),
                'api_calls': stats['api_calls'],
                'last_call_at': stats['last_call_at'].strftime('%Y-%m-%d %H:%M:%S') if stats['last_call_at'] else 'Never',
                'runtime_minutes': round((datetime.now() - stats['registered_at']).total_seconds() / 60, 2)
            }
            for stats in self.caller_stats.values()
        ]
    
    def get_full_report(self) -> Dict:
        """获取完整的资源报告"""
        return {
            'memory': self.get_memory_usage(),
            'api_stats': self.get_api_stats(),
            'callers': self.get_caller_stats(),
            'report_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def print_full_report(self, logger=None):
        """打印资源使用报告"""
        report = self.get_full_report()

        # 使用传入的logger，如果没有则使用print
        log_func = logger.info if logger else print

        log_func("="*50)
        log_func("📊 Exchange资源使用报告")
        log_func("="*50)
        
        # 内存使用
        memory = report['memory']
        log_func(f"💾 进程内存使用:")
        log_func(f"   初始内存: {memory['initial_memory_mb']} MB (占系统 {memory['initial_vs_system_percent']}%)")
        log_func(f"   当前内存: {memory['current_memory_mb']} MB (占系统 {memory['current_vs_system_percent']}%)")
        log_func(f"   峰值内存: {memory['peak_memory_mb']} MB (占系统 {memory['peak_vs_system_percent']}%)")
        log_func(f"   当前增长: {memory['memory_increase_mb']} MB ({memory['memory_increase_percent']}%)")
        log_func(f"   峰值增长: {memory['peak_increase_mb']} MB ({memory['peak_increase_percent']}%)")
        log_func("")
        log_func(f"🖥️  系统内存状态:")
        log_func(f"   总内存: {memory['system_total_gb']} GB")
        log_func(f"   不可用内存: {memory['system_unavailable_gb']} GB ({memory['system_usage_percent']}%)")
        log_func(f"   ├─ 已使用: {memory['system_used_gb']} GB")
        log_func(f"   └─ 缓存等: {memory['system_unavailable_gb'] - memory['system_used_gb']:.2f} GB")
        log_func(f"   可用内存: {memory['system_available_gb']} GB")
        log_func("")        
        # API统计
        api_stats = report['api_stats']
        log_func(f"🌐 API调用统计:")
        log_func(f"   总调用次数: {api_stats['total_api_calls']}")
        log_func(f"   运行时间: {api_stats['runtime_hours']} 小时")
        log_func(f"   平均调用频率: {api_stats['calls_per_hour']} 次/小时")
        log_func(f"   最近1分钟: {api_stats['calls_last_minute']} 次")
        log_func(f"   调用者数量: {api_stats['caller_count']}")
        log_func("")
        # 调用者详情
        log_func(f"🔄 API调用者详情:")
        for caller in report['callers']:
            log_func(f"   {caller['name']}: {caller['api_calls']} 次调用, 运行 {caller['runtime_minutes']} 分钟")

        log_func("="*50)

# 全局监控实例
resource_monitor = ResourceMonitor()

# 便捷函数
def register_caller(caller_name: str):
    """注册API调用者"""
    resource_monitor.register_caller(caller_name)

def record_api_call(caller_name: str, api_method: str):
    """记录API调用"""
    resource_monitor.record_api_call(caller_name, api_method)

def get_basic_memory_info():
    """获取基础内存信息（轻量级）"""
    return resource_monitor.get_basic_memory_info()

def print_resource_report(logger=None):
    """打印资源报告"""
    resource_monitor.print_full_report(logger)

def get_resource_report():
    """获取资源报告"""
    return resource_monitor.get_full_report()

async def periodic_memory_monitor(logger=None):
    """定期内存监控任务"""
    # 使用传入的logger，如果没有则使用print
    log_func = logger.info if logger else print
    log_warning = logger.warning if logger else print
    log_error = logger.error if logger else print

    # 从配置获取监控参数
    enable_monitoring = get_app_config('app.memory.enable_monitoring', True)
    check_interval = get_app_config('app.memory.check_interval', 300)  # 5分钟
    warning_threshold = get_app_config('app.memory.warning_threshold', 80)  # 80%
    critical_threshold = get_app_config('app.memory.critical_threshold', 90)  # 90%

    if not enable_monitoring:
        log_func("📊 内存监控已禁用")
        return

    log_func(f"📊 启动定期内存监控 - 间隔{check_interval}秒, 警告阈值{warning_threshold}%, 严重阈值{critical_threshold}%")

    try:
        while True:
            await asyncio.sleep(check_interval)

            # 获取内存使用情况
            memory_info = resource_monitor.get_memory_usage()
            system_usage = memory_info['system_usage_percent']
            current_memory = memory_info['current_memory_mb']
            peak_memory = memory_info['peak_memory_mb']

            # 检查系统内存使用率
            if system_usage >= critical_threshold:
                log_error(f"🚨 系统内存使用严重: {system_usage}% (当前进程: {current_memory}MB, 峰值: {peak_memory}MB)")
            elif system_usage >= warning_threshold:
                log_warning(f"⚠️ 系统内存使用警告: {system_usage}% (当前进程: {current_memory}MB, 峰值: {peak_memory}MB)")
            log_func(f"📊 定期内存监控: 系统{system_usage}%, 进程{current_memory}MB, 峰值{peak_memory}MB")

    except asyncio.CancelledError:
        log_func("📊 定期内存监控任务被取消")
        raise
    except Exception as e:
        log_error(f"📊 内存监控任务异常: {e}")
        raise
