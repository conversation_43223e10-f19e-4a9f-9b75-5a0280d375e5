import re
import random
from tabulate import tabulate
from datetime import datetime, timedelta
import pandas as pd
from pandas.tseries.frequencies import to_offset

# 全局标志，用于确保只有第一个被取消的任务输出换行符
_ctrl_c_newline_printed = False

def print_ctrl_c_newline():
    """
    为Ctrl+C输出换行符，确保只有第一个被取消的任务输出换行符
    避免^C与日志共行，同时避免多个空行
    """
    global _ctrl_c_newline_printed
    if not _ctrl_c_newline_printed:
        print()
        _ctrl_c_newline_printed = True

# def format_volume(volume):
#     """
#     将成交量格式化为更易读的形式（如 1.0M, 1.5K, 2.3B）
#     """
#     if volume >= 1_000_000_000:
#         return f"{volume / 1_000_000_000:.1f}B"  # 十亿
#     elif volume >= 1_000_000:
#         return f"{volume / 1_000_000:.1f}M"  # 百万
#     elif volume >= 1_000:
#         return f"{volume / 1_000:.1f}K"  # 千
#     else:
#         return f"{volume}"  # 小于1000
    
def format_volume(volume):
    """
    将成交量格式化为更易读的形式（如 1M, 1.5K, 2B）。
    """
    if volume >= 1_000_000_000:
        return f"{int(volume / 1_000_000_000)}B" if volume % 1_000_000_000 == 0 else f"{volume / 1_000_000_000:.1f}B"  # 十亿
    elif volume >= 1_000_000:
        return f"{int(volume / 1_000_000)}M" if volume % 1_000_000 == 0 else f"{volume / 1_000_000:.1f}M"  # 百万
    elif volume >= 1_000:
        return f"{int(volume / 1_000)}K" if volume % 1_000 == 0 else f"{volume / 1_000:.1f}K"  # 千
    else:
        return f"{int(volume)}"  # 小于1000
    
def parse_interval(interval):
    # 将时间框架字符串转换为秒数
        match = re.match(r'^(\d+)([smhdw])$', interval)
        if not match:
            raise ValueError(f"无效的时间框架: {interval}")
        amount = int(match.group(1))
        unit = match.group(2)
        unit_multiplier = {
            's': 1,
            'm': 60,
            'h': 3600,
            'd': 86400,
            'w': 604800,
        }[unit]
        return amount * unit_multiplier
    
# def get_sleep_time(interval):
#     """
#     根据 interval 返回一个随机的稍微小点的 sleep 时间范围，但还是会有点频繁 不够精准
#     """
#     interval_seconds = parse_interval(interval)
#     min_sleep = int(interval_seconds * 0.4)
#     max_sleep = int(interval_seconds * 0.5)
#     return random.randint(min_sleep, max_sleep)

def get_sleep_time(current_timestamp, timeframe):
    """
    根据 timeframe 在收盘后3秒钟醒来，更精准
    """
    interval_seconds = parse_interval(timeframe)
    interval_millis = interval_seconds * 1000

    # 计算下一个K线闭合时间
    next_close_time = ((current_timestamp // interval_millis) + 1) * interval_millis
    # bj = pd.to_datetime(next_close_time, unit='ms') + pd.Timedelta(hours=8)  # 转为北京时间
    # print(f"下一根 {timeframe} K线的开盘时间: {bj}")
    # 目标时间为闭合后3秒
    target_time = next_close_time + 3000

    # 计算需要休眠的时间（秒）
    sleep_millis = target_time - current_timestamp
    sleep_seconds = sleep_millis / 1000.0

    # 确保休眠时间非负
    return max(0, sleep_seconds)


def format_time(seconds):
    """
    将秒数转换为更易读的格式（如 m 或 h）
    """
    if seconds < 60:
        return f"{seconds}s"
    elif seconds < 3600:
        return f"{seconds // 60}m"
    else:
        return f"{seconds // 3600}h"


# 格式化表格并添加缩进
def format_dataframe_with_tabulate(df, indent=8):
    """
    使用 tabulate 格式化 Pandas DataFrame 并添加缩进。
    
    参数:
        df: pd.DataFrame - 要格式化的 DataFrame
        indent: int - 缩进的空格数量
    
    返回:
        str - 格式化后的表格字符串
    """
    # df = df.reset_index()  # 确保索引可见
    table = tabulate(df, headers="keys", tablefmt="pretty")
    # 添加缩进
    indented_table = "\n".join(f"{' ' * indent}{line}" for line in table.split("\n"))
    return indented_table

def convert_interval_to_pandas_freq(interval: str) -> str:
    """
    将交易所的时间间隔格式转换为 pandas 兼容的偏移别名（适配 pandas >= 2.2）
    Binance 示例: 'm' → 'min', 'h' → 'h', 'd' → 'D', 'M' → 'ME'
    """
    unit = interval[-1]
    value = interval[:-1]
    
    mapping = {
        'm': 'min',  # 分钟 (Binance 'm' → pandas 'min')
        'M': 'ME', # 月份 (Binance 'M' → pandas 'ME')
        'h': 'h',  # 小时
        'd': 'D',  # 天
        'w': 'W'   # 周
    }
    
    converted_unit = mapping.get(unit, unit)
    return f"{value}{converted_unit}"


def get_closed_candle_index(ohlcv_df, interval):
    """
    根据时间间隔判断最新 K 线是否已收盘
    :param ohlcv_df: 包含 timestamp 列的 DataFrame
    :param interval: K 线间隔（如 '1h'）
    :return: 已收盘 K 线的索引（-1 或 -2）
    """
    # 获取最新 K 线的时间戳（确保带时区）
    latest_candle_time = ohlcv_df['timestamp'].iloc[-1]

    # 转换时间间隔为 pandas 兼容格式
    pandas_interval = convert_interval_to_pandas_freq(interval)
    
    # 计算下一个 K 线周期的开始时间
    freq = to_offset(pandas_interval)
    next_candle_start = latest_candle_time + freq
    
    # 获取当前时间（与 K 线时区一致）
    current_time = pd.Timestamp.now(tz=latest_candle_time.tz)
    
    # 判断当前是否在最新 K 线周期内
    if current_time < next_candle_start:
        return -2  # 最新 K 线未收盘，取前一根
    else:
        return -1  # 最新 K 线已收盘