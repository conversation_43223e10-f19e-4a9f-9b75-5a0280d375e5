"""配置管理器 - 统一管理所有配置文件"""

import yaml
import os
from typing import Dict, Any, Optional
from pathlib import Path

class ConfigManager:
    """配置管理器 - 单例模式"""
    
    _instance = None
    _configs = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ConfigManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self.config_dir = Path("config")
            self._verbose = False  # 默认不显示加载信息
            self._initialized = True
            self._load_all_configs()
            # 加载完配置后，根据配置决定是否显示加载信息
            self._update_verbose_setting()
    
    def _load_all_configs(self):
        """加载所有配置文件"""
        config_files = {
            'app': 'app_config.yaml',
            'monitor': 'monitor_config.yaml',
            'exchange': 'exchange_config.yaml',
            'telegram': 'telegram_config.yaml',
            'trader': 'trader_config.yaml'
        }
        
        for config_name, filename in config_files.items():
            self._load_config(config_name, filename)
    
    def _load_config(self, config_name: str, filename: str):
        """加载单个配置文件"""
        config_path = self.config_dir / filename

        try:
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    self._configs[config_name] = yaml.safe_load(f)
                if self._verbose:
                    print(f"✅ 已加载配置文件: {config_path}")
            else:
                if self._verbose:
                    print(f"⚠️ 配置文件不存在: {config_path}")
                self._configs[config_name] = {}
        except Exception as e:
            if self._verbose:
                print(f"❌ 加载配置文件失败 {config_path}: {e}")
            self._configs[config_name] = {}
    
    def get_config(self, config_name: str, key_path: str = None, default: Any = None) -> Any:
        """获取配置值

        Args:
            config_name: 配置文件名 (app, monitor, exchange, telegram)
            key_path: 配置键路径，如 'monitor.volume.threshold_multiplier'
            default: 默认值

        Returns:
            配置值
        """
        if config_name not in self._configs:
            return default
            
        config = self._configs[config_name]
        
        if key_path is None:
            return config
            
        # 支持嵌套键路径，如 'monitor.volume.threshold_multiplier'
        keys = key_path.split('.')
        current = config
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return default
                
        return current
    
    def reload_config(self, config_name: str):
        """重新加载指定配置文件"""
        config_files = {
            'app': 'app_config.yaml',
            'monitor': 'monitor_config.yaml',
            'exchange': 'exchange_config.yaml',
            'telegram': 'telegram_config.yaml',
            'trader': 'trader_config.yaml'
        }
        
        if config_name in config_files:
            self._load_config(config_name, config_files[config_name])
        else:
            raise ValueError(f"未知的配置名称: {config_name}")
    
    def reload_all_configs(self):
        """重新加载所有配置文件"""
        self._load_all_configs()
    
    def get_app_config(self, key_path: str = None, default: Any = None) -> Any:
        """获取应用配置"""
        return self.get_config('app', key_path, default)
    
    def get_monitor_config(self, key_path: str = None, default: Any = None) -> Any:
        """获取监控配置"""
        return self.get_config('monitor', key_path, default)
    
    def get_exchange_config(self, key_path: str = None, default: Any = None) -> Any:
        """获取交易所配置"""
        return self.get_config('exchange', key_path, default)



    def get_telegram_config(self, key_path: str = None, default: Any = None) -> Any:
        """获取Telegram配置"""
        return self.get_config('telegram', key_path, default)

    def get_trader_config(self, key_path: str = None, default: Any = None) -> Any:
        """获取交易配置"""
        return self.get_config('trader', key_path, default)

    def set_verbose(self, verbose: bool):
        """设置是否显示配置加载信息"""
        self._verbose = verbose

    def _update_verbose_setting(self):
        """根据配置更新verbose设置"""
        try:
            show_config_loading = self.get_app_config('app.ui.show_config_loading', True)
            self._verbose = show_config_loading
        except:
            # 如果获取配置失败，保持默认设置
            pass
    
    def print_all_configs(self):
        """打印所有配置"""
        print("📋 当前所有配置:")
        for config_name, config_data in self._configs.items():
            print(f"\n🔧 {config_name.upper()} 配置:")
            self._print_dict(config_data, indent=2)
    
    def _print_dict(self, data: dict, indent: int = 0):
        """递归打印字典"""
        for key, value in data.items():
            if isinstance(value, dict):
                print(" " * indent + f"{key}:")
                self._print_dict(value, indent + 2)
            else:
                print(" " * indent + f"{key}: {value}")

# 全局配置管理器实例
config_manager = ConfigManager()

# 便捷函数
def get_app_config(key_path: str = None, default: Any = None) -> Any:
    """获取应用配置"""
    return config_manager.get_app_config(key_path, default)

def get_monitor_config(key_path: str = None, default: Any = None) -> Any:
    """获取监控配置"""
    return config_manager.get_monitor_config(key_path, default)

def get_exchange_config(key_path: str = None, default: Any = None) -> Any:
    """获取交易所配置"""
    return config_manager.get_exchange_config(key_path, default)



def get_telegram_config(key_path: str = None, default: Any = None) -> Any:
    """获取Telegram配置"""
    return config_manager.get_telegram_config(key_path, default)

def get_trader_config(key_path: str = None, default: Any = None) -> Any:
    """获取交易配置"""
    return config_manager.get_trader_config(key_path, default)

def reload_all_configs():
    """重新加载所有配置"""
    config_manager.reload_all_configs()

def print_all_configs():
    """打印所有配置"""
    config_manager.print_all_configs()
