"""Exchange管理器 - 统一管理所有任务的exchange实例"""

import asyncio
import os
import ccxt.pro as ccxtpro
from dotenv import load_dotenv
from typing import Optional
from .resource_monitor import resource_monitor, get_basic_memory_info
from .config_manager import get_exchange_config

# 加载环境变量
load_dotenv()

class GlobalExchangeManager:
    """全局Exchange管理器，为所有任务提供统一的exchange实例"""
    
    def __init__(self):
        self._exchange: Optional[ccxtpro.binance] = None
        self._markets_loaded = False
        self._initialization_lock = asyncio.Lock()
        self._is_initializing = False
        
    async def get_exchange(self, logger=None) -> ccxtpro.binance:
        """获取全局exchange实例，如果不存在则创建"""
        if self._exchange is None:
            async with self._initialization_lock:
                if self._exchange is None:  # 双重检查
                    await self._create_exchange(logger)
        return self._exchange
    
    async def _create_exchange(self, logger=None):
        """创建exchange实例"""
        # 使用传入的logger记录错误，如果没有则使用print
        log_error = logger.error if logger else print
        log_info = logger.info if logger else print

        self._is_initializing = True

        # 检查是否启用详细日志
        verbose_init = get_exchange_config('exchange.binance.verbose_init', False)

        if verbose_init:
            log_info("🔄 Exchange初始化...")

        try:
            # 从配置文件获取exchange配置
            enable_rate_limit = get_exchange_config('exchange.binance.enable_rate_limit', True)
            timeout = get_exchange_config('exchange.binance.timeout', 30000)
            https_proxy = get_exchange_config('exchange.binance.proxy.https', None)
            ws_proxy = get_exchange_config('exchange.binance.proxy.ws', None)

            # 构建exchange配置
            exchange_config = {
                'apiKey': os.getenv("API_KEY"),
                'secret': os.getenv("API_SECRET"),
                'enableRateLimit': enable_rate_limit,
                'timeout': timeout,
            }

            # 添加代理配置（如果配置了的话）
            if https_proxy:
                exchange_config['https_proxy'] = https_proxy
            if ws_proxy:
                exchange_config['ws_proxy'] = ws_proxy

            if verbose_init:
                log_info(f"🔧 Exchange配置: 限流={enable_rate_limit}, 超时={timeout}ms, HTTPS代理={https_proxy}, WS代理={ws_proxy}")

            # 创建exchange实例
            self._exchange = ccxtpro.binance(exchange_config)

            # 预加载markets信息
            await self._exchange.load_markets()
            self._markets_loaded = True

            # 关键节点监控：exchange初始化后检查内存（轻量级）
            memory_after_exchange = get_basic_memory_info()
            log_info(f"✅ Exchange初始化完成")
            if verbose_init:
                log_info(f"📊 Exchange初始化后内存: {memory_after_exchange['current_memory_mb']} MB (峰值: {memory_after_exchange['peak_memory_mb']} MB)")

        except Exception as e:
            log_error(f"❌ 全局Exchange实例创建失败: {e}")
            self._exchange = None
            raise
        finally:
            self._is_initializing = False
    
    def record_api_call(self, caller_name: str, method_name: str):
        """记录API调用"""
        resource_monitor.record_api_call(caller_name, method_name)
    
    async def close(self):
        """关闭exchange连接"""
        if self._exchange:
            try:
                print("🔒 正在关闭exchange连接...")
                await self._exchange.close()
                print("🔒 Exchange连接已关闭")
            except Exception as e:
                print(f"关闭exchange连接时出错: {e}")
            finally:
                self._exchange = None
                self._markets_loaded = False
    
    @property
    def is_ready(self) -> bool:
        """检查exchange是否就绪"""
        return self._exchange is not None and self._markets_loaded
    
    @property
    def is_initializing(self) -> bool:
        """检查是否正在初始化"""
        return self._is_initializing

# 创建全局实例
global_exchange_manager = GlobalExchangeManager()

async def get_global_exchange(logger=None) -> ccxtpro.binance:
    """获取全局exchange实例的便捷函数"""
    return await global_exchange_manager.get_exchange(logger)

def record_api_call(caller_name: str, method_name: str):
    """记录API调用的便捷函数"""
    global_exchange_manager.record_api_call(caller_name, method_name)

async def close_global_exchange():
    """关闭全局exchange的便捷函数"""
    await global_exchange_manager.close()
