import logging
import os
from pathlib import Path
from logging.handlers import RotatingFileHandler
# 移除对logging_config的依赖，使用简化的硬编码配置

def setup_logger(log_name, log_file):
    """
    配置日志记录器

    params:
        log_name: str - 日志记录器名称，用于区分不同模块或功能。
        log_file: str - 日志输出文件名。
    return:
        配置完成的日志记录器

    注意：
        - log_name 和 log_file 都是必需参数，不提供默认值
        - 这确保每个模块都明确指定自己的日志文件名
        - 避免产生无用的默认日志文件
    """
    # 简化配置：使用合理的默认值
    # logging 模块的日志级别从高到低排列如下：
    #     CRITICAL (50) - 最严重的问题
    #     ERROR (40) - 错误
    #     WARNING (30) - 警告
    #     INFO (20) - 一般信息
    #     DEBUG (10) - 调试信息
    #     NOTSET (0) - 未设置级别

    # 创建日志记录器
    logger = logging.getLogger(log_name)
    logger.setLevel(logging.DEBUG)  # 设置全局日志级别

    if not logger.hasHandlers():  # 避免重复添加处理器

        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)  # 控制台只显示 INFO 及以上级别日志

        # 处理日志文件路径 - 确保放到logs目录下
        if not os.path.isabs(log_file):  # 如果不是绝对路径
            # 获取项目根目录
            project_root = Path(__file__).parent.parent
            logs_dir = project_root / "logs"

            # 确保logs目录存在
            logs_dir.mkdir(exist_ok=True)

            # 构建完整的日志文件路径
            log_file = logs_dir / log_file

        # 创建文件处理器
        file_handler = RotatingFileHandler(
            str(log_file),
            maxBytes=5 * 1024 * 1024,  # 文件大小限制为 5MB
            backupCount=30,             # 最多保留 30 个备份文件
            encoding="utf-8"
        )
        file_handler.setLevel(logging.DEBUG)  # 文件记录 DEBUG 及以上级别日志

        # 设置自定义命名规则, 默认滚动生成的文件名是"xxx.log.1", 希望改为"xxx-1-2024-06-24-14-30-15.log"
        def custom_namer(default_name):
            from datetime import datetime

            # 获取当前时间戳
            timestamp = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")

            # 解析默认文件名，提取序号
            # 默认格式: "corsair_engine.log.1" -> 我们要改为 "corsair_engine-1-2024-06-24-14-30-15.log"
            if ".log." in default_name:
                base_name, sequence = default_name.rsplit(".log.", 1)
                return f"{base_name}-{sequence}-{timestamp}.log"
            else:
                # 如果格式不符合预期，回退到简单格式
                return default_name.replace(".log.", "-") + ".log"

        # 设置自定义命名函数
        file_handler.namer = custom_namer

        # 定义日志格式
        formatter = logging.Formatter("%(asctime)s - %(levelname)-7s - %(message)s", "%Y-%m-%d %H:%M:%S")
        console_handler.setFormatter(formatter)
        file_handler.setFormatter(formatter)

        # 将处理器添加到记录器
        logger.addHandler(console_handler)
        logger.addHandler(file_handler)

    return logger

def log_block(logger, title, message):
    """
    打印和记录格式化的内容块
    
    参数:
        logger: logging.Logger - 日志记录器实例。
        message: str - 要记录的内容。
    """
    block = f"""{message}"""
    logger.info(f"{title}: {block}")