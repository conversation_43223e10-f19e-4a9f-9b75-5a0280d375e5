"""Logo显示工具"""

from utils.config_manager import get_app_config

def _detect_script_startup():
    """检测是否由脚本启动"""
    import os

    # 检查环境变量
    if os.getenv('QUANT_START_CMD') is not None:
        return True

    # 检查是否启用简洁模式（用户可以通过配置控制）
    clean_console = get_app_config('app.ui.clean_console', True)
    return clean_console

def show_logo(logger=None):
    """显示程序logo
    
    Args:
        logger: 可选的logger实例，如果提供则同时记录到日志
    """
    # 从配置获取应用信息
    app_name = get_app_config('app.info.name', 'Quant Trading System')
    app_version = get_app_config('app.info.version', '1.0.0')
    app_description = get_app_config('app.info.description', '量化交易监控系统')
    
    # ANSI颜色代码
    CYAN = '\033[0;36m'
    BLUE = '\033[0;34m'
    GREEN = '\033[0;32m'
    NC = '\033[0m'  # No Color
    
    logo_lines = [
        f"{CYAN}",
        "  ██████  ██    ██  █████  ███    ██ ████████ ",
        " ██    ██ ██    ██ ██   ██ ████   ██    ██    ",
        " ██    ██ ██    ██ ███████ ██ ██  ██    ██    ",
        " ██ ▄▄ ██ ██    ██ ██   ██ ██  ██ ██    ██    ",
        "  ██████   ██████  ██   ██ ██   ████    ██    ",
        "     ▀▀                                       ",
        f"{NC}",
        f"{BLUE}        {app_name} v{app_version}{NC}",
        f"{GREEN}        {app_description}{NC}",
        "==================================================",
    ]
    
    # 打印到控制台
    for line in logo_lines:
        print(line)
    
    # 如果提供了logger，也记录到日志（去除颜色代码）
    if logger:
        clean_lines = [
            "",
            "  ██████  ██    ██  █████  ███    ██ ████████ ",
            " ██    ██ ██    ██ ██   ██ ████   ██    ██    ",
            " ██    ██ ██    ██ ███████ ██ ██  ██    ██    ",
            " ██ ▄▄ ██ ██    ██ ██   ██ ██  ██ ██    ██    ",
            "  ██████   ██████  ██   ██ ██   ████    ██    ",
            "     ▀▀                                       ",
            "",
            f"        {app_name} v{app_version}",
            f"        {app_description}",
            "==================================================",
        ]
        
        for line in clean_lines:
            logger.info(line)

def get_simple_logo():
    """获取简化版logo（纯文本，用于Telegram等）"""
    app_name = get_app_config('app.info.name', 'Quant Trading System')
    app_version = get_app_config('app.info.version', '1.0.0')
    
    return f"""
🤖 {app_name} v{app_version}
"""

def show_startup_banner(logger=None, skip_logo=False):
    """显示启动横幅（包含logo和系统信息）

    Args:
        logger: 可选的logger实例
        skip_logo: 是否跳过logo显示（避免重复显示）
    """
    import platform
    import sys
    from datetime import datetime

    # 检查是否启用logo显示
    show_logo_enabled = get_app_config('app.ui.show_logo', True)
    show_system_info_enabled = get_app_config('app.ui.show_system_info', True)

    # 检查是否由脚本启动（避免重复显示logo）
    started_by_script = _detect_script_startup()

    if show_logo_enabled and not skip_logo and not started_by_script:
        show_logo(logger)
    elif started_by_script:
        # 如果由脚本启动，只在日志中记录简化信息，不在控制台重复显示
        if logger:
            app_name = get_app_config('app.info.name', 'Quant Trading System')
            app_version = get_app_config('app.info.version', '1.0.0')
            logger.info(f"🚀 {app_name} v{app_version} 启动")

    # 系统信息
    if show_system_info_enabled:
        system_info = [
            f"🖥️  系统: {platform.system()} {platform.release()}",
            f"🐍 Python: {sys.version.split()[0]}",
            f"📅 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        ]

        if started_by_script:
            # 如果由脚本启动，只记录到日志，不在控制台显示
            if logger:
                for line in system_info:
                    logger.info(line)
        else:
            # 如果直接启动，在控制台和日志都显示
            print("")
            for line in system_info:
                print(line)
                if logger:
                    logger.info(line)
            print("")
