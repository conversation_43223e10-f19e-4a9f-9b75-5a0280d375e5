@echo off
REM CorsairQuant程序启动脚本 (Windows)
REM 使用方法：
REM   start_quant.bat          # 普通启动

REM 切换到项目根目录
cd /d "%~dp0\.."

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请确保Python已安装并在PATH中
    pause
    exit /b 1
)

REM 检查corsair_engine.py是否存在
if not exist "corsair_engine.py" (
    echo ❌ 错误: 未找到corsair_engine.py文件
    pause
    exit /b 1
)

echo 🚀 启动CorsairQuant程序...
set QUANT_START_CMD=python
python corsair_engine.py
