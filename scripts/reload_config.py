#!/usr/bin/env python3
"""
配置重载脚本
用于手动重载配置文件，无需重启程序
"""

import sys
import os
import argparse

# 添加项目根目录到 sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config_manager import config_manager
from utils.utils_logger import setup_logger

# 设置日志
logger = setup_logger("reload_config", "reload_config.log")


def reload_config(config_type: str = None):
    """重载配置文件
    
    Args:
        config_type: 配置类型 ('app', 'monitor', 'exchange', 'telegram', 'trader', 'all' 或 None)
    """
    try:
        if config_type is None or config_type == 'all':
            logger.info("🔄 重新加载所有配置文件...")
            config_manager.reload_all_configs()
            logger.info("✅ 所有配置文件重载完成")
        else:
            logger.info(f"🔄 重新加载 {config_type} 配置文件...")
            config_manager.reload_config(config_type)
            logger.info(f"✅ {config_type} 配置文件重载完成")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置重载失败: {e}")
        return False


def _show_summary_all_config():
    """显示所有配置的关键信息摘要"""
    from utils.config_manager import get_app_config, get_monitor_config, get_exchange_config, get_telegram_config, get_trader_config

    logger.info("📱 应用配置:")
    enabled_monitors = get_app_config('app.tasks.enabled_monitors', [])
    enabled_traders = get_app_config('app.tasks.enabled_traders', [])
    memory_threshold = get_app_config('app.memory.warning_threshold', 80)
    logger.info(f"    启用的监控: {enabled_monitors}")
    logger.info(f"    启用的交易: {enabled_traders}")
    logger.info(f"    内存警告阈值: {memory_threshold}%")

    logger.info("📊 监控配置:")
    volume_threshold = get_monitor_config('monitor.volume.threshold_multiplier', 2.0)
    pressure_threshold = get_monitor_config('monitor.pressure.strong_buy_threshold', 70.0)
    logger.info(f"    成交量阈值倍数: {volume_threshold}")
    logger.info(f"    强买压阈值: {pressure_threshold}%")

    logger.info("🔗 交易所配置:")
    retry_count = get_exchange_config('exchange.retry.max_retries', 3)
    logger.info(f"    最大重试次数: {retry_count}")

    logger.info("📱 Telegram配置:")
    stop_commands = get_telegram_config('telegram.commands.stop', [])
    logger.info(f"    停止命令数量: {len(stop_commands)}")

    logger.info("💰 交易配置:")
    max_position_ratio = get_trader_config('trader.common.risk_management.max_position_ratio', 0.5)
    leverage = get_trader_config('trader.common.trading.leverage', 5)
    logger.info(f"    最大仓位比例: {max_position_ratio}")
    logger.info(f"    杠杆倍数: {leverage}")


def _show_detailed_all_config():
    """显示所有配置的详细信息 - 动态读取配置文件"""
    import yaml
    from pathlib import Path

    config_dir = Path("config")
    config_files = {
        "📱 应用配置": "app_config.yaml",
        "📊 监控配置": "monitor_config.yaml",
        "🔗 交易所配置": "exchange_config.yaml",
        "📱 Telegram配置": "telegram_config.yaml",
        "💰 交易配置": "trader_config.yaml"
    }

    for section_name, config_file in config_files.items():
        config_path = config_dir / config_file
        if config_path.exists():
            logger.info(f"{section_name} (详细):")
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                _display_config_section(config_data, indent="    ")
            except Exception as e:
                logger.error(f"    ❌ 读取配置文件失败: {e}")
        else:
            logger.warning(f"    ⚠️  配置文件不存在: {config_file}")


def _display_config_section(data, indent="", max_depth=3, current_depth=0):
    """递归显示配置节点"""
    if current_depth >= max_depth:
        return

    if isinstance(data, dict):
        for key, value in data.items():
            if isinstance(value, dict):
                # 如果是字典，显示键名并递归显示内容
                if current_depth < max_depth - 1:
                    logger.info(f"{indent}{key}:")
                    _display_config_section(value, indent + "  ", max_depth, current_depth + 1)
            elif isinstance(value, list):
                # 如果是列表，显示列表内容
                if len(value) <= 10:  # 只显示较短的列表
                    logger.info(f"{indent}{key}: {value}")
                else:
                    logger.info(f"{indent}{key}: [列表包含{len(value)}项]")
            else:
                # 基本类型直接显示
                # 添加一些常见的单位后缀
                display_value = _format_config_value(key, value)
                logger.info(f"{indent}{key}: {display_value}")
    elif isinstance(data, list):
        if len(data) <= 10:
            logger.info(f"{indent}{data}")
        else:
            logger.info(f"{indent}[列表包含{len(data)}项]")
    else:
        display_value = _format_config_value("", data)
        logger.info(f"{indent}{display_value}")


def _format_config_value(key, value):
    """格式化配置值，添加合适的单位"""
    key_lower = key.lower()

    # 时间相关的配置项
    if any(time_word in key_lower for time_word in ['timeout', 'interval', 'delay', 'duration']):
        if isinstance(value, (int, float)) and value > 0:
            return f"{value}秒"

    # 百分比相关的配置项
    if any(percent_word in key_lower for percent_word in ['threshold', 'ratio', 'percentage']):
        if isinstance(value, (int, float)) and 0 <= value <= 1:
            return f"{value * 100}%"
        elif isinstance(value, (int, float)) and value > 1:
            return f"{value}%"

    # 大小相关的配置项
    if any(size_word in key_lower for size_word in ['size', 'length', 'limit']):
        if 'message' in key_lower and isinstance(value, int):
            return f"{value}字符"
        elif 'order' in key_lower and isinstance(value, (int, float)):
            return f"{value}USDT"

    # 频率相关的配置项
    if 'requests_per_minute' in key_lower:
        return f"{value}次/分钟"

    return value


def show_current_config(config_type: str = None, detailed: bool = False):
    """显示当前配置"""
    from utils.config_manager import get_app_config, get_monitor_config, get_exchange_config, get_telegram_config, get_trader_config
    
    logger.info("📋 当前配置状态:")
    
    if config_type is None or config_type == 'all':
        if detailed:
            # 详细模式：显示所有配置项
            _show_detailed_all_config()
        else:
            # 默认模式：只显示关键配置项
            _show_summary_all_config()

    elif config_type == 'app':
        enabled_monitors = get_app_config('app.tasks.enabled_monitors', [])
        enabled_traders = get_app_config('app.tasks.enabled_traders', [])
        memory_threshold = get_app_config('app.memory.warning_threshold', 80)
        logger.info(f"    启用的监控: {enabled_monitors}")
        logger.info(f"    启用的交易: {enabled_traders}")
        logger.info(f"    内存警告阈值: {memory_threshold}%")
        
    elif config_type == 'monitor':
        volume_threshold = get_monitor_config('monitor.volume.threshold_multiplier', 2.0)
        pressure_threshold = get_monitor_config('monitor.pressure.strong_buy_threshold', 70.0)
        oi_threshold = get_monitor_config('monitor.open_interest.threshold_percentage', 5.0)
        logger.info(f"    成交量阈值倍数: {volume_threshold}")
        logger.info(f"    强买压阈值: {pressure_threshold}%")
        logger.info(f"    持仓量变动阈值: {oi_threshold}%")
        
    elif config_type == 'exchange':
        retry_count = get_exchange_config('exchange.retry.max_retries', 3)
        timeout = get_exchange_config('exchange.timeout.request_timeout', 30)
        logger.info(f"    最大重试次数: {retry_count}")
        logger.info(f"    请求超时时间: {timeout}秒")
        
    elif config_type == 'telegram':
        stop_commands = get_telegram_config('telegram.commands.stop', [])
        status_commands = get_telegram_config('telegram.commands.status', [])
        reload_commands = get_telegram_config('telegram.commands.reload', [])
        logger.info(f"    停止命令: {stop_commands}")
        logger.info(f"    状态命令: {status_commands}")
        logger.info(f"    重载命令: {reload_commands}")

    elif config_type == 'trader':
        max_position_ratio = get_trader_config('trader.common.risk_management.max_position_ratio', 0.5)
        stop_loss_ratio = get_trader_config('trader.common.risk_management.stop_loss_ratio', 0.02)
        leverage = get_trader_config('trader.common.trading.leverage', 5)
        ema_period = get_trader_config('trader.ema_strategy.ema_period', 20)
        logger.info(f"    最大仓位比例: {max_position_ratio}")
        logger.info(f"    止损比例: {stop_loss_ratio}")
        logger.info(f"    杠杆倍数: {leverage}")
        logger.info(f"    EMA周期: {ema_period}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="配置重载脚本 - 重载配置文件无需重启程序",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python scripts/reload_config.py                    # 重载所有配置
  python scripts/reload_config.py -t monitor         # 重载监控配置
  python scripts/reload_config.py -t app             # 重载应用配置
  python scripts/reload_config.py -s                 # 只显示当前配置
  python scripts/reload_config.py -t monitor -s      # 显示监控配置并重载

支持的配置类型:
  app       - 应用主配置 (app_config.yaml)
  monitor   - 监控配置 (monitor_config.yaml)
  exchange  - 交易所配置 (exchange_config.yaml)
  telegram  - Telegram配置 (telegram_config.yaml)
  trader    - 交易配置 (trader_config.yaml)
  all       - 所有配置 (默认)
        """
    )
    
    parser.add_argument(
        '-t', '--type',
        choices=['app', 'monitor', 'exchange', 'telegram', 'trader', 'all'],
        default='all',
        help='指定要重载的配置类型 (默认: all)'
    )
    
    parser.add_argument(
        '-s', '--show',
        action='store_true',
        help='显示当前配置状态'
    )
    
    parser.add_argument(
        '--no-reload',
        action='store_true',
        help='只显示配置，不执行重载'
    )

    parser.add_argument(
        '-d', '--detailed',
        action='store_true',
        help='显示详细配置信息'
    )

    args = parser.parse_args()

    # 根据操作类型显示不同的启动消息
    if args.no_reload:
        logger.info("📋 配置查看脚本启动")
    else:
        logger.info("🔧 配置重载脚本启动")

    # 显示当前配置
    if args.show or args.no_reload:
        show_current_config(args.type, args.detailed)

    # 执行重载
    if not args.no_reload:
        success = reload_config(args.type)
        
        if success:
            logger.info("🎉 配置重载操作完成")
            
            # 重载后再次显示配置（如果指定了显示选项）
            if args.show:
                logger.info("📋 重载后的配置状态:")
                show_current_config(args.type)
        else:
            logger.error("💥 配置重载失败")
            sys.exit(1)
    
    logger.info("✅ 脚本执行完成")


if __name__ == "__main__":
    main()
