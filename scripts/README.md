# 管理脚本目录

这个目录包含了量化程序的所有管理脚本。

## 📋 脚本列表

### macOS/Linux 脚本
- `start_quant.sh` - 启动程序脚本
- `stop_quant.sh` - 停止程序脚本  
- `status_quant.sh` - 状态查询脚本

### Windows 脚本
- `start_quant.bat` - 启动程序脚本
- `stop_quant.bat` - 停止程序脚本

## 🚀 使用方法

### 直接使用脚本
```bash
# 启动程序
./scripts/start_quant.sh caffeinate

# 查看状态
./scripts/status_quant.sh

# 停止程序
./scripts/stop_quant.sh
```

### 通过主入口使用（推荐）
```bash
# 使用根目录的主入口脚本
./quant.sh start caffeinate
./quant.sh status
./quant.sh stop
```

## 📖 详细说明

详细的使用说明请参考：[../docs/SCRIPTS_README.md](../docs/SCRIPTS_README.md)

## 📝 注意事项

1. 所有脚本都需要从项目根目录执行
2. 脚本会自动切换到正确的工作目录
3. 推荐使用根目录的 `./quant.sh` 作为主入口
