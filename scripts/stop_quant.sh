#!/bin/bash

# 量化程序停止脚本
# 使用方法：
#   ./stop_quant.sh           # 优雅停止（推荐）
#   ./stop_quant.sh force     # 强制停止
#   ./stop_quant.sh telegram  # 通过Telegram停止

# 获取脚本所在目录，然后切换到项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_ROOT"

# 从配置文件读取配置值
get_config_value() {
    local key="$1"
    local default="$2"
    python3 -c "
import sys
sys.path.append('$PROJECT_ROOT')
from utils.config_manager import get_app_config
print(get_app_config('$key', '$default'))
" 2>/dev/null || echo "$default"
}

# 读取配置
GRACEFUL_TIMEOUT=$(get_config_value "app.process.graceful_shutdown_timeout" "60")
TELEGRAM_TIMEOUT=$(get_config_value "app.cleanup.telegram_timeout" "60")

# 锁文件路径
LOCK_FILE="$PROJECT_ROOT/corsair_quant.lock"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查程序是否在运行
check_running() {
    if [ -f "$LOCK_FILE" ]; then
        local pid=$(cat "$LOCK_FILE" 2>/dev/null)
        if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
            return 0  # 程序正在运行
        else
            # 锁文件存在但进程不存在，清理锁文件
            rm -f "$LOCK_FILE"
            return 1  # 程序未运行
        fi
    else
        return 1  # 程序未运行
    fi
}

# 优雅停止
graceful_stop() {
    echo -e "${BLUE}🛑 正在优雅停止量化程序...${NC}"
    
    if ! check_running; then
        echo -e "${YELLOW}⚠️  量化程序未在运行${NC}"
        return 0
    fi
    
    local pid=$(cat "$LOCK_FILE" 2>/dev/null)
    echo -e "${BLUE}📋 发送SIGTERM信号到进程 $pid${NC}"
    
    # 发送SIGTERM信号
    if kill -TERM "$pid" 2>/dev/null; then
        echo -e "${BLUE}⏳ 等待程序优雅退出...${NC}"
        
        # 等待最多配置的超时时间
        local count=0
        while [ $count -lt $GRACEFUL_TIMEOUT ] && check_running; do
            sleep 1
            count=$((count + 1))
            if [ $((count % 5)) -eq 0 ]; then
                echo -e "${BLUE}⏳ 等待中... (${count}/${GRACEFUL_TIMEOUT}秒)${NC}"
            fi
        done

        if check_running; then
            echo -e "${YELLOW}⚠️  程序未在${GRACEFUL_TIMEOUT}秒内退出，可以尝试强制停止: ./stop_quant.sh force${NC}"
            return 1
        else
            echo -e "${GREEN}✅ 量化程序已优雅停止${NC}"
            return 0
        fi
    else
        echo -e "${RED}❌ 无法发送停止信号到进程 $pid${NC}"
        return 1
    fi
}

# 强制停止
force_stop() {
    echo -e "${RED}💥 正在强制停止量化程序...${NC}"
    
    if ! check_running; then
        echo -e "${YELLOW}⚠️  量化程序未在运行${NC}"
        return 0
    fi
    
    local pid=$(cat "$LOCK_FILE" 2>/dev/null)
    echo -e "${RED}📋 发送SIGKILL信号到进程 $pid${NC}"
    
    # 发送SIGKILL信号
    if kill -KILL "$pid" 2>/dev/null; then
        sleep 2
        
        if check_running; then
            echo -e "${RED}❌ 强制停止失败${NC}"
            return 1
        else
            echo -e "${GREEN}✅ 量化程序已强制停止${NC}"
            # 清理锁文件
            rm -f "$LOCK_FILE"
            return 0
        fi
    else
        echo -e "${RED}❌ 无法发送强制停止信号到进程 $pid${NC}"
        return 1
    fi
}

# 通过Telegram停止
telegram_stop() {
    echo -e "${BLUE}📱 通过Telegram停止量化程序...${NC}"
    echo -e "${YELLOW}📋 请在Telegram中发送以下命令之一:${NC}"
    echo -e "${YELLOW}   /stop${NC}"
    echo -e "${YELLOW}   停止程序${NC}"
    echo -e "${YELLOW}   stop${NC}"
    echo ""
    echo -e "${BLUE}⏳ 等待Telegram停止命令...${NC}"
    
    # 等待程序停止
    local count=0
    while [ $count -lt $TELEGRAM_TIMEOUT ] && check_running; do
        sleep 1
        count=$((count + 1))
        if [ $((count % 10)) -eq 0 ]; then
            echo -e "${BLUE}⏳ 等待Telegram停止命令... (${count}/${TELEGRAM_TIMEOUT}秒)${NC}"
        fi
    done

    if check_running; then
        echo -e "${YELLOW}⚠️  ${TELEGRAM_TIMEOUT}秒内未收到Telegram停止命令，可以尝试其他停止方式${NC}"
        return 1
    else
        echo -e "${GREEN}✅ 量化程序已通过Telegram停止${NC}"
        return 0
    fi
}

# 显示帮助
show_help() {
    echo -e "${BLUE}量化程序停止脚本${NC}"
    echo ""
    echo -e "${YELLOW}使用方法:${NC}"
    echo -e "  ${GREEN}./stop_quant.sh${NC}           # 优雅停止（推荐）"
    echo -e "  ${GREEN}./stop_quant.sh force${NC}     # 强制停止"
    echo -e "  ${GREEN}./stop_quant.sh telegram${NC}  # 通过Telegram停止"
    echo -e "  ${GREEN}./stop_quant.sh help${NC}      # 显示帮助"
    echo ""
    echo -e "${YELLOW}停止方式说明:${NC}"
    echo -e "  ${BLUE}优雅停止${NC}: 发送SIGTERM信号，等待程序自然退出"
    echo -e "  ${BLUE}强制停止${NC}: 发送SIGKILL信号，立即终止程序"
    echo -e "  ${BLUE}Telegram停止${NC}: 等待用户通过Telegram发送停止命令"
}

# 主逻辑
case "$1" in
    "force")
        force_stop
        ;;
    "telegram")
        telegram_stop
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    "")
        graceful_stop
        ;;
    *)
        echo -e "${RED}❌ 未知参数: $1${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac
