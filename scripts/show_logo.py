#!/usr/bin/env python3
"""独立的logo显示脚本，供shell脚本调用"""

import sys
from pathlib import Path

# 添加项目根目录到路径
script_dir = Path(__file__).parent
project_root = script_dir.parent
sys.path.append(str(project_root))

def main():
    """主函数"""
    try:
        # 重定向stdout来隐藏配置加载信息
        import sys
        from io import StringIO

        # 临时捕获输出
        old_stdout = sys.stdout
        sys.stdout = StringIO()

        try:
            from utils.utils_logo import show_logo
            # 恢复stdout
            sys.stdout = old_stdout
            show_logo()
        except:
            # 恢复stdout
            sys.stdout = old_stdout
            raise

    except ImportError:
        # 如果导入失败，显示简化版本
        print("⚔️🏴‍☠️ CorsairQuant 量化交易程序 ⚓⛵")
        print("═" * 50)
    except Exception as e:
        # 其他错误，静默处理
        print("⚔️🏴‍☠️ CorsairQuant 量化交易程序 ⚓⛵")
        print("═" * 50)

if __name__ == "__main__":
    main()
