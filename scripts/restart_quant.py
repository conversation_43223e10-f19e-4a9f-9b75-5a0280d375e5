#!/usr/bin/env python3
"""
CorsairQuant程序重启脚本
用于安全地重启CorsairQuant程序，确保资源正确清理
"""

import time
import subprocess
import sys
import os
import signal
import psutil
from pathlib import Path

def find_quant_processes():
    """查找正在运行的CorsairQuant程序进程"""
    processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = proc.info['cmdline']
            if cmdline and any('corsair_engine.py' in arg for arg in cmdline):
                # 检查是否是CorsairQuant程序
                if any('CorsairQuant' in arg for arg in cmdline) or any('corsair_engine.py' in arg for arg in cmdline):
                    processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return processes

def graceful_stop_processes(processes, timeout=60):
    """优雅地停止进程"""
    if not processes:
        print("没有找到正在运行的量化程序进程")
        return True
    
    print(f"找到 {len(processes)} 个量化程序进程，正在优雅停止...")
    
    # 发送SIGTERM信号
    for proc in processes:
        try:
            print(f"向进程 {proc.pid} 发送停止信号...")
            proc.terminate()
        except psutil.NoSuchProcess:
            continue
    
    # 等待进程退出
    start_time = time.time()
    while time.time() - start_time < timeout:
        alive_processes = []
        for proc in processes:
            try:
                if proc.is_running():
                    alive_processes.append(proc)
            except psutil.NoSuchProcess:
                continue
        
        if not alive_processes:
            print("✅ 所有进程已优雅退出")
            return True
        
        print(f"等待 {len(alive_processes)} 个进程退出...")
        time.sleep(2)
    
    # 如果还有进程存活，强制杀死
    alive_processes = []
    for proc in processes:
        try:
            if proc.is_running():
                alive_processes.append(proc)
                print(f"强制杀死进程 {proc.pid}")
                proc.kill()
        except psutil.NoSuchProcess:
            continue
    
    if alive_processes:
        print(f"⚠️ 强制杀死了 {len(alive_processes)} 个进程")
        time.sleep(2)  # 等待进程完全退出
    
    return True

def restart_quant(start_cmd=None, use_caffeinate=False):
    """重启CorsairQuant程序"""
    print("🔄 开始重启CorsairQuant程序...")
    
    # 1. 查找并停止现有进程
    processes = find_quant_processes()
    if not graceful_stop_processes(processes):
        print("❌ 停止现有进程失败")
        return False
    
    # 2. 额外等待，确保资源完全释放
    print("⏳ 等待资源释放...")
    time.sleep(5)
    
    # 3. 构建启动命令
    if start_cmd:
        cmd = start_cmd.split()
    else:
        # 默认启动命令
        python = sys.executable
        script_dir = Path(__file__).parent.parent
        main_script = script_dir / "corsair_engine.py"
        
        if use_caffeinate:
            cmd = ['caffeinate', python, str(main_script)]
        else:
            cmd = [python, str(main_script)]
    
    # 4. 设置环境变量
    env = os.environ.copy()
    env['QUANT_RESTART'] = '1'
    
    # 5. 启动新进程
    try:
        print(f"🚀 启动新进程: {' '.join(cmd)}")
        process = subprocess.Popen(
            cmd,
            env=env,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            start_new_session=True  # 创建新的进程组
        )
        
        # 等待一下确保进程启动
        time.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print(f"✅ CorsairQuant程序重启成功，新进程PID: {process.pid}")
            return True
        else:
            print(f"❌ CorsairQuant程序启动失败，退出码: {process.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ 启动新进程失败: {e}")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='量化程序重启脚本')
    parser.add_argument('--cmd', help='自定义启动命令')
    parser.add_argument('--caffeinate', action='store_true', help='使用caffeinate启动')
    parser.add_argument('--timeout', type=int, default=60, help='停止进程的超时时间（秒）')
    
    args = parser.parse_args()
    
    print("=" * 50)
    print("🔄 量化程序重启脚本")
    print("=" * 50)
    
    success = restart_quant(
        start_cmd=args.cmd,
        use_caffeinate=args.caffeinate
    )
    
    if success:
        print("✅ 重启完成")
        sys.exit(0)
    else:
        print("❌ 重启失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
