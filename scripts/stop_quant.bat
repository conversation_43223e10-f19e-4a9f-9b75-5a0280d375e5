@echo off
REM 量化程序停止脚本 (Windows)
REM 使用方法：
REM   stop_quant.bat           # 优雅停止（推荐）
REM   stop_quant.bat force     # 强制停止
REM   stop_quant.bat telegram  # 通过Telegram停止

REM 切换到项目根目录
cd /d "%~dp0\.."

set LOCK_FILE=%~dp0..\corsair_quant.lock

REM 检查参数
if "%1"=="force" goto force_stop
if "%1"=="telegram" goto telegram_stop
if "%1"=="help" goto show_help
if "%1"=="-h" goto show_help
if "%1"=="--help" goto show_help
if not "%1"=="" (
    echo ❌ 未知参数: %1
    echo.
    goto show_help
)

REM 默认优雅停止
goto graceful_stop

:check_running
if not exist "%LOCK_FILE%" (
    set RUNNING=0
    goto :eof
)

for /f %%i in (%LOCK_FILE%) do set PID=%%i
tasklist /FI "PID eq %PID%" 2>nul | find /I "%PID%" >nul
if errorlevel 1 (
    del "%LOCK_FILE%" 2>nul
    set RUNNING=0
) else (
    set RUNNING=1
)
goto :eof

:graceful_stop
echo 🛑 正在优雅停止量化程序...

call :check_running
if %RUNNING%==0 (
    echo ⚠️  量化程序未在运行
    goto end
)

echo 📋 正在停止进程 %PID%...

REM 使用taskkill优雅停止
taskkill /PID %PID% /T >nul 2>&1
if errorlevel 1 (
    echo ❌ 无法停止进程 %PID%
    goto end
)

echo ⏳ 等待程序优雅退出...

REM 等待最多60秒
set /a count=0
:wait_loop
if %count% geq 60 goto wait_timeout
call :check_running
if %RUNNING%==0 goto graceful_success
timeout /t 1 /nobreak >nul
set /a count+=1
if %count% geq 5 (
    set /a display_count=%count%
    if %count% geq 10 if %count% geq 15 if %count% geq 20 if %count% geq 25 (
        echo ⏳ 等待中... (%count%/60秒)
    )
)
goto wait_loop

:wait_timeout
echo ⚠️  程序未在60秒内退出，可以尝试强制停止: scripts\stop_quant.bat force
goto end

:graceful_success
echo ✅ 量化程序已优雅停止
goto end

:force_stop
echo 💥 正在强制停止量化程序...

call :check_running
if %RUNNING%==0 (
    echo ⚠️  量化程序未在运行
    goto end
)

echo 📋 强制停止进程 %PID%...

REM 使用taskkill强制停止
taskkill /PID %PID% /T /F >nul 2>&1
if errorlevel 1 (
    echo ❌ 强制停止失败
    goto end
)

timeout /t 2 /nobreak >nul
call :check_running
if %RUNNING%==0 (
    echo ✅ 量化程序已强制停止
    del "%LOCK_FILE%" 2>nul
) else (
    echo ❌ 强制停止失败
)
goto end

:telegram_stop
echo 📱 通过Telegram停止量化程序...
echo 📋 请在Telegram中发送以下命令之一:
echo    /stop
echo    停止程序
echo    stop
echo.
echo ⏳ 等待Telegram停止命令...

set /a count=0
:telegram_wait_loop
if %count% geq 60 goto telegram_timeout
call :check_running
if %RUNNING%==0 goto telegram_success
timeout /t 1 /nobreak >nul
set /a count+=1
set /a mod=%count% %% 10
if %mod%==0 (
    echo ⏳ 等待Telegram停止命令... (%count%/60秒)
)
goto telegram_wait_loop

:telegram_timeout
echo ⚠️  60秒内未收到Telegram停止命令，可以尝试其他停止方式
goto end

:telegram_success
echo ✅ 量化程序已通过Telegram停止
goto end

:show_help
echo 量化程序停止脚本
echo.
echo 使用方法:
echo   stop_quant.bat           # 优雅停止（推荐）
echo   stop_quant.bat force     # 强制停止
echo   stop_quant.bat telegram  # 通过Telegram停止
echo   stop_quant.bat help      # 显示帮助
echo.
echo 停止方式说明:
echo   优雅停止: 发送停止信号，等待程序自然退出
echo   强制停止: 立即终止程序进程
echo   Telegram停止: 等待用户通过Telegram发送停止命令
goto end

:end
pause
