# Telegram机器人配置文件
telegram:
  commands:
    # 停止程序命令
    stop:
      - /stop          # 标准停止命令
      - /quit          # 退出命令
      - /shutdown      # 关闭命令
      - /exit          # 退出命令
      - 停止程序        # 中文停止
      - 关闭程序        # 中文关闭
      - 退出程序        # 中文退出
      - 结束程序        # 中文结束
      - stop           # 英文停止
      - quit           # 英文退出
      - shutdown       # 英文关闭
      - exit           # 英文退出
    
    # 状态查询命令
    status:
      - /status        # 标准状态命令
      - /info          # 信息命令
      - /health        # 健康检查
      - 状态           # 中文状态
      - 运行状态        # 中文运行状态
      - 程序状态        # 中文程序状态

    # 重启程序命令
    restart:
      - /restart       # 标准重启命令
      - /reboot        # 重启命令
      - 重启程序        # 中文重启
      - 重新启动        # 中文重新启动
      - restart        # 英文重启
      - reboot         # 英文重启

    # 重载配置命令
    reload:
      - /reload        # 重载所有配置
      - /reload_all    # 重载所有配置
      - /reload_config # 重载配置
      - 重载配置        # 中文重载配置
      - 刷新配置        # 中文刷新配置
      - reload         # 英文重载

    # 重载特定配置命令
    reload_monitor:
      - /reload_monitor    # 重载监控配置
      - 重载监控配置        # 中文重载监控配置

    reload_app:
      - /reload_app        # 重载应用配置
      - 重载应用配置        # 中文重载应用配置

    reload_trader:
      - /reload_trader     # 重载交易配置
      - 重载交易配置        # 中文重载交易配置

    reload_exchange:
      - /reload_exchange   # 重载交易所配置
      - 重载交易所配置      # 中文重载交易所配置

  # 回复消息配置
  messages:
    stop_reply: "🛑 收到停止指令，量化程序即将关闭..."
    status_reply: "✅ 量化程序运行正常，所有任务正在执行中"
    restart_reply: "🔄 收到重启指令，量化程序即将重启..."
    reload_reply: "🔄 收到重载指令，正在重新加载所有配置..."
    reload_monitor_reply: "🔄 收到重载指令，正在重新加载监控配置..."
    reload_app_reply: "🔄 收到重载指令，正在重新加载应用配置..."
    reload_trader_reply: "🔄 收到重载指令，正在重新加载交易配置..."
    reload_exchange_reply: "🔄 收到重载指令，正在重新加载交易所配置..."
    reload_success_reply: "✅ 配置重载完成"
    unknown_reply: "❓ 未知命令，请发送 /status 查看程序状态"
