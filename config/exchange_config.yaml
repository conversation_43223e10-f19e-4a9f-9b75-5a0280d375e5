# 交易所配置文件
exchange:
  # Binance配置
  binance:
    name: "binance"
    enable_rate_limit: true
    timeout: 30000              # 超时时间(毫秒)
    verbose_init: false         # 是否显示详细初始化信息
    
    # 代理配置
    proxy:
      http: "http://127.0.0.1:7890"
      https: "http://127.0.0.1:7890"
      ws: "http://127.0.0.1:7890"
    
    # API限制配置
    rate_limits:
      requests_per_second: 10   # 每秒请求数限制
      requests_per_minute: 1200 # 每分钟请求数限制
      
    # 重试配置
    retry:
      max_retries: 3            # 最大重试次数
      retry_delay: 1.0          # 重试延迟(秒)
      backoff_factor: 2.0       # 退避因子
      
    # 错误处理配置
    error_handling:
      exchange_down_waiting: 60 # 交易所宕机等待时间(秒)
      network_error_delay: 5    # 网络错误延迟(秒)
      
  # 通用配置
  common:
    default_exchange: "binance"
    sandbox_mode: false         # 是否使用沙盒模式
    
    # 数据获取配置
    data_fetch:
      default_limit: 100        # 默认获取数据条数
      max_limit: 1000          # 最大获取数据条数
      
    # 缓存配置
    cache:
      enable: true             # 是否启用缓存
      ttl: 60                  # 缓存生存时间(秒)
