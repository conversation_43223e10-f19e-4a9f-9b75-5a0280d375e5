# 交易配置文件
trader:
  # 通用交易配置
  common:
    symbols:
      - "BTC/USDT:USDT"
      # - "ETH/USDT:USDT"  # 可以取消注释启用ETH交易
    timeframes:
      - "5m"
      - "15m"
      - "1h"
    
    # 风险管理
    risk_management:
      max_position_ratio: 0.5      # 最大仓位比例
      stop_loss_ratio: 0.02        # 止损比例
      take_profit_ratio: 0.04      # 止盈比例
      max_daily_loss: 0.05         # 每日最大亏损比例
      
    # 交易参数
    trading:
      leverage: 5                  # 杠杆倍数
      tick_size: 0.2              # 价格变动最小单位
      min_order_size: 10          # 最小订单金额(USDT)
      max_order_size: 1000        # 最大订单金额(USDT)
      
  # EMA交易配置
  ema_strategy:
    ema_period: 20                 # EMA周期
    entry_k_count: 4               # 起始K之后需要确认的K线数量
    pullback_tolerance: 0.5        # 回踩容忍度(%)
    trend_confirmation_bars: 3     # 趋势确认K线数
    
    # 进场条件
    entry_conditions:
      volume_multiplier: 1.5       # 成交量倍数要求
      rsi_oversold: 30            # RSI超卖线
      rsi_overbought: 70          # RSI超买线
      
    # 出场条件
    exit_conditions:
      profit_target: 0.03         # 盈利目标
      trailing_stop: 0.015        # 移动止损
      time_stop_hours: 24         # 时间止损(小时)
      
  # 情绪交易配置
  sentiment_strategy:
    sentiment_threshold: 0.7       # 情绪阈值
    volume_confirmation: true      # 是否需要成交量确认
    social_weight: 0.3            # 社交媒体权重
    news_weight: 0.4              # 新闻权重
    technical_weight: 0.3         # 技术指标权重
    
  # 订单簿交易配置
  orderbook_strategy:
    depth_levels: 10              # 订单簿深度层级
    imbalance_threshold: 0.6      # 不平衡阈值
    large_order_threshold: 50000  # 大单阈值(USDT)
    
    # 买卖压力分析
    pressure_analysis:
      strong_buy_threshold: 70.0   # 强买压阈值(%)
      strong_sell_threshold: 30.0  # 强卖压阈值(%)
      pressure_duration: 300       # 压力持续时间(秒)
      
  # 网格交易配置
  grid_strategy:
    grid_count: 10                # 网格数量
    grid_spacing: 0.005           # 网格间距(%)
    base_order_size: 100          # 基础订单金额(USDT)
    profit_per_grid: 0.002        # 每格利润(%)
    
  # 套利交易配置
  arbitrage_strategy:
    min_profit_threshold: 0.001   # 最小套利利润阈值
    max_position_time: 3600       # 最大持仓时间(秒)
    exchanges:
      - "binance"
      - "okx"
      # - "bybit"  # 可以取消注释启用
      
  # 交易执行配置
  execution:
    order_type: "limit"           # 订单类型 (limit/market)
    slippage_tolerance: 0.001     # 滑点容忍度
    retry_attempts: 3             # 重试次数
    retry_delay: 1.0              # 重试延迟(秒)
    
    # 订单管理
    order_management:
      cancel_timeout: 30          # 订单取消超时(秒)
      partial_fill_threshold: 0.8 # 部分成交阈值
      order_refresh_interval: 60  # 订单刷新间隔(秒)
      
  # 回测配置
  backtest:
    start_date: "2024-01-01"      # 回测开始日期
    end_date: "2024-12-31"        # 回测结束日期
    initial_balance: 10000        # 初始资金(USDT)
    commission_rate: 0.001        # 手续费率
    
  # 性能监控
  performance:
    track_pnl: true               # 是否跟踪盈亏
    track_drawdown: true          # 是否跟踪回撤
    track_sharpe_ratio: true      # 是否跟踪夏普比率
    report_interval: 3600         # 报告间隔(秒)
    
  # 日志配置
  logging:
    log_trades: true              # 是否记录交易日志
    log_signals: true             # 是否记录信号日志
    log_performance: true         # 是否记录性能日志
    log_level: "INFO"             # 日志级别
    
  # 通知配置
  notifications:
    trade_notifications: true     # 交易通知
    signal_notifications: false   # 信号通知
    performance_notifications: true # 性能通知
    error_notifications: true     # 错误通知
