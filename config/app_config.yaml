# 应用配置文件
app:
  # 应用基本信息
  info:
    name: "CorsairQuant Trading System"
    version: "1.0.0"
    description: "海盗量化交易监控系统"
    
  # 任务配置
  tasks:
    # 启用的监控任务
    enabled_monitors:
      - "monitor1_volume"
      - "monitor2_pressure"
      # - "monitor3_divergence"  # 临时禁用
      - "monitor4_oi"
      - "monitor5_ema_ma_crossover"
      # - "monitor6_volume_oi_matrix"  # 临时禁用
      - "monitor7_bollinger_squeeze"
      
    # 启用的交易任务(目前注释掉)
    enabled_traders: []
    # - "trader1_ema"
    # - "trader3_orderbook"
    
    # 任务启动配置
    startup:
      delay_between_tasks: 0.5  # 任务间启动延迟(秒)
      max_startup_time: 60      # 最大启动时间(秒)
      
  # 内存监控配置
  memory:
    enable_monitoring: true     # 是否启用内存监控
    check_interval: 300        # 检查间隔(秒)
    warning_threshold: 85      # 内存使用警告阈值(%)
    critical_threshold: 92     # 内存使用严重阈值(%)
    
  # 进程管理配置
  process:
    single_instance: true      # 是否只允许单实例运行
    graceful_shutdown_timeout: 60  # 优雅关闭超时时间(秒)
    restart_delay: 3           # 重启前等待时间(秒)

  # 清理配置
  cleanup:
    telegram_timeout: 60       # Telegram停止命令等待超时时间(秒)
    exchange_timeout: 10       # Exchange清理超时时间(秒)
    message_queue_timeout: 60  # 消息队列清空超时时间(秒)
    process_kill_wait: 5       # 进程终止后等待时间(秒)
    max_log_size_mb: 50        # 日志文件清理阈值(MB)
    
  # 错误处理配置
  error_handling:
    max_consecutive_errors: 5   # 最大连续错误数
    error_cooldown: 60         # 错误冷却时间(秒)
    auto_restart: false        # 是否自动重启
    
  # 性能配置
  performance:
    max_concurrent_tasks: 20   # 最大并发任务数
    task_timeout: 300          # 任务超时时间(秒)
    gc_interval: 3600          # 垃圾回收间隔(秒)
    
  # 开发配置
  development:
    debug_mode: false          # 是否开启调试模式
    profile_performance: false # 是否启用性能分析
    mock_exchange: false       # 是否使用模拟交易所

  # Telegram消息配置
  telegram:
    divider_length: 32         # 分隔符长度
    final_message_delay: 0.5   # 最后消息发送延迟(秒)

  # 界面配置
  ui:
    show_logo: true            # 是否显示启动logo
    show_system_info: true     # 是否显示系统信息
    clean_console: true        # 是否启用简洁控制台输出（避免重复显示logo和系统信息）
    show_config_loading: false # 是否显示配置加载信息
