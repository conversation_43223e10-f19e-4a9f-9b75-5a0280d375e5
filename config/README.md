# 配置文件说明

本目录包含了量化交易系统的所有配置文件。

## 📁 配置文件结构

```
config/
├── app_config.yaml          # 应用主配置
├── monitor_config.yaml      # 监控配置
├── exchange_config.yaml     # 交易所配置
├── telegram_config.yaml     # Telegram机器人配置
├── trader_config.yaml       # 交易配置
└── README.md               # 本说明文件
```

## 🔧 配置文件详解

### 1. app_config.yaml - 应用主配置

- **应用信息**: 名称、版本、描述
- **任务配置**: 启用的监控和交易任务
- **内存监控**: 内存使用阈值和检查间隔
- **进程管理**: 单实例运行、优雅关闭等
- **错误处理**: 错误重试和冷却配置
- **性能配置**: 并发任务数、超时时间等

### 2. monitor_config.yaml - 监控配置

- **通用配置**: 监控的交易对和时间间隔
- **成交量监控**: 阈值倍数、回看周期
- **买卖压力监控**: 强买压/卖压阈值
- **背离监控**: RSI参数、背离检测配置
- **持仓量监控**: OI变动阈值
- **技术指标**: EMA/MA交叉、布林带等参数

### 3. exchange_config.yaml - 交易所配置

- **Binance配置**: API限制、代理设置
- **重试机制**: 最大重试次数、延迟配置
- **错误处理**: 交易所宕机等待时间
- **缓存配置**: 数据缓存TTL设置

### 4. telegram_config.yaml - Telegram配置

- **命令配置**: 停止命令、状态查询命令、重载命令
- **回复消息**: 各种情况的回复内容

### 5. trader_config.yaml - 交易配置

- **通用交易配置**: 交易对、时间框架、风险管理
- **交易参数**: EMA交易、情绪交易、订单簿交易等
- **交易执行**: 订单类型、滑点容忍度、重试机制
- **性能监控**: 盈亏跟踪、回撤监控、报告配置

## 🚀 使用方法

### 基本用法

```python
from utils.config_manager import get_app_config, get_monitor_config

# 获取应用配置
app_name = get_app_config('app.info.name')
enabled_monitors = get_app_config('app.tasks.enabled_monitors')

# 获取监控配置
volume_threshold = get_monitor_config('monitor.volume.threshold_multiplier')
```

### 高级用法

```python
from utils.config_manager import config_manager

# 获取完整配置
full_config = config_manager.get_config('app')

# 重新加载配置
config_manager.reload_config('monitor')

# 打印所有配置
config_manager.print_all_configs()
```

## ⚙️ 配置修改指南

### 1. 修改监控阈值

编辑 `monitor_config.yaml`：

```yaml
monitor:
  volume:
    threshold_multiplier: 3.0  # 修改成交量阈值
  pressure:
    strong_buy_threshold: 75.0  # 修改强买压阈值
```

### 2. 添加新的监控任务

编辑 `app_config.yaml`：

```yaml
app:
  tasks:
    enabled_monitors:
      - "monitor1_volume"
      - "monitor_new_feature"  # 添加新监控
```

### 3. 添加Telegram命令

编辑 `telegram_config.yaml`：

```yaml
telegram:
  commands:
    stop:
      - "/stop"
      - "关机"  # 添加新的停止命令
```

## 🔄 配置热重载

系统支持配置热重载，无需重启程序：

```python
# 重新加载单个配置
config_manager.reload_config('monitor')

# 重新加载所有配置
config_manager.reload_all_configs()
```

## ⚠️ 注意事项

1. **YAML格式**: 注意缩进和语法正确性
2. **数据类型**: 确保配置值的数据类型正确
3. **默认值**: 所有配置都有默认值，缺失配置不会导致程序崩溃
4. **敏感信息**: 敏感信息（如API密钥）应放在 `.env` 文件中，不要写在配置文件里
5. **版本控制**: 配置文件应该提交到版本控制系统

## 🧪 测试配置

运行测试脚本验证配置：

```bash
python tests/test_config_manager.py
```

## 📝 配置模板

如需添加新的配置文件，请参考现有文件的格式和结构。
