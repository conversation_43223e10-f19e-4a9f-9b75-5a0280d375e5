"""交易：抓趋势：通过站上或跌下均线来开仓和平仓"""
# 站上了，就开仓做多，（如果有空单就平掉空单）
# 跌下了，就开仓做空，（如果有多单就平掉多单）

import sys
import asyncio
import ccxt
import ccxt.pro as ccxtpro
import pandas as pd
import textwrap
import traceback
import math
from dotenv import load_dotenv
import os
from ccxt.base.errors import ExchangeNotAvailable,RequestTimeout,NetworkError

# 添加项目根目录到 sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.utils_logger import setup_logger,log_block
from utils.errors import CustomError, ErrorCode
from utils.utils_telegram import send_telegram_message
from utils.utils_exchange_error import handle_exchange_error
from utils.constants import TASK_DIVIDER, EXCHANGE_DOWN_WAITING
from utils.utils_common import parse_interval, get_sleep_time, format_time, format_dataframe_with_tabulate, print_ctrl_c_newline
from utils.exchange_wrapper import create_exchange_wrapper

# 初始化日志记录器
current_file_name = os.path.splitext(os.path.basename(__file__))[0]
log_name = f"{current_file_name}_logger"
log_file_name = f"{current_file_name}.log"
logger = setup_logger(log_name=log_name,log_file=log_file_name)


# 加载.env文件, 读取bot_token和chat_id
load_dotenv()
TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN_TRADER")
TELEGRAM_CHAT_ID = os.getenv("TELEGRAM_CHAT_ID")

# 使用全局exchange包装器
exchange = create_exchange_wrapper(current_file_name, logger)

# todo: 收益控制（主动止盈：固定收益率止盈or固定金额止盈）
# todo: 风险控制（被动止损：固定收益率止损or固定金额止损）
# todo: 心跳监控（单独开一个心跳监控线程？）

# 交易参数，可根据需要进行修改
trade_symbol = "BTC/USDT:USDT"
timeframe = "5m"
ema_period = 20
entry_k_count = 4  # 起始K之后第几跟K开始入场，值要>=2（也就是起始K之后要等 entry_k_count-1 根K线确认才能入场）
leverage = 5   # 初始杠杆倍数
position_size_ratio = 0.5  # 50%仓位，每次开仓比例
tick_size_custom = 0.2  # 需要>=价格变动的最小单位，因为流动性的关系，建议设置的比tick_size大一点，就trade_symbol="BTC/USDT:USDT"来说，建议设置为0.5、1、6、8、12
retries_count = 3  # 请求超时后的重试次数
stop_loss = 0.02  # 单笔最大亏损，也就是价格反向波动多少就止损（比如 0.1 就是价格反向波动 10% 就止损）

#  ******** 👇下面的数据都不要修改👇 ********

class GlobalState:
    def __init__(self):
        # 定义全局变量: 仓位状态
        self.has_long_position = False
        self.has_short_position = False
        self.has_position = False        # 是否有持仓（不管是不是本币种，其他币种也算）
        self.has_other_position = False  # 预留，暂时用不到，不用关心此值
        self.long_contracts = 0.0       # 多头持仓的合约数量，单位是币(不是usdt)
        self.short_contracts = 0.0      # 空头持仓的合约数量，单位是币(不是usdt)

        # 定义全局变量: 挂单状态
        self.has_open_long = False   # 是否有挂单开多
        self.has_open_short = False  # 是否有挂单开空
        self.has_close_long = False  # 是否有挂单平多
        self.has_close_short = False # 是否有挂单平空
        self.has_order = False       # 是否有挂单（不管是不是本币种，其他币种也算）
        self.has_other_order = False # 预留，暂时用不到，不用关心此值

        # 交易所是否宕机状态
        self.exchange_is_down = False

        # 定义全局变量: 存放历史K线数据
        self.ohlcv_data = pd.DataFrame()

        # 定义全局变量: 上次ws数据的一些状态，用于与这次ws数据进行比较
        self.last_status = {
            'last_low': None,               # 上次从ws获取到的K线的最低价
            'last_high': None,              # 上次从ws获取到的K线的最高价
            'last_start_index': None,       # 上次计算得到的起始K线索引
        }

global_state = GlobalState()  # 创建全局状态实例

def reset_global_state():
    global global_state
    global_state = GlobalState()  # 重新创建实例

# 定义常量: 
EMA_COLUMN = f"ema{ema_period}"

#  ******** 👆上面的数据都不要修改👆 ********

def get_local_time():
    """获取本地时间（北京时间）"""
    try:
        local_time = pd.Timestamp.now(tz='Asia/Shanghai')
        return local_time
    except Exception as e:
        logger.error(f"获取本地时间报错: {e}")
        raise

async def fetch_server_time():
    """获取服务器时间（北京时间）"""
    try:
        server_time = await exchange.fetch_time()  # 获取服务器时间（毫秒时间戳）
        server_time_bj = pd.to_datetime(server_time, unit='ms') + pd.Timedelta(hours=8)  # 转为北京时间
        return server_time_bj
    except Exception as e:
        logger.error(f"获取服务器时间报错: {e}")
        raise

async def check_balance_usdt():
    try:
        # type='spot': 获取现货账户余额。
        # type='margin': 获取杠杆账户余额。
        # type='future': 获取 U 本位期货账户余额。
        # type='delivery': 获取币本位期货账户余额。
        balance = await exchange.fetch_balance({'type': 'future'})
        usdt_balance_free = balance['free']['USDT']
        # 检查账户可用余额是否足够
        if usdt_balance_free < 10:  # 可用余额不足10美金就别玩了🙂
            logger.error(f"可用余额不足10美金, 可用余额: {usdt_balance_free} USDT")
            return False
        logger.info(f"可用余额: {usdt_balance_free} USDT")
        return True
    except Exception as e:
        logger.error(f"获取余额报错: {e}")
        raise

async def enough_balance_to_order(current_price,trigger_price):
    try:
        # 获取账户余额
        balance = await exchange.fetch_balance({'type': 'future'})
        usdt_balance = balance['free']['USDT']
        
        # 获取市场最小交易量和最小订单金额
        min_amount, min_cost, contract_type = await get_market_min_order(trade_symbol)
        
        current_leverage = await exchange.fetch_leverage(trade_symbol)
        # logger.debug(f"current_leverage={current_leverage}")
        
        # 计算交易金额
        # position_size = usdt_balance * position_size_ratio  # 计划投入的 USDT 数量
        position_size = usdt_balance * position_size_ratio * current_leverage['longLeverage']  # 计划投入的 USDT 数量, 合约交易需要再乘以杠杆
        logger.debug(f"position_size={position_size}")
        logger.debug(f"current_price={current_price}")
        logger.debug(f"trigger_price={trigger_price}")
        precision = int(-math.log10(min_amount))  # 计算精度
        logger.debug(f"precision={precision}")
        
        if contract_type == "USDT":  # U 本位期货
            # 计算合约数量（以 USDT 为单位）
            position_size_in_contracts = round(position_size / current_price, precision)  # 合约数量 = 仓位金额 / 当前价格
            order_value = position_size  # 订单金额就是仓位大小（单位是 USDT）
            logger.debug(f"position_size_in_contracts={position_size_in_contracts}")
            logger.debug(f"order_value={order_value}")
    
        elif contract_type == "COIN":  # 币本位期货
            # 计算合约数量（例如 BTC 合约数量）
            position_size_in_contracts = round(position_size / current_price, precision)  # 合约数量 = USDT 金额 / 当前价格
            order_value = position_size_in_contracts * current_price  # 订单金额 = 合约数量 * 当前价格
    
        else:
            logger.error(f"无法识别的合约类型: {contract_type}")
            return None
    
        # 检查订单金额是否满足最小要求
        if position_size_in_contracts < min_amount:
            logger.error(f"交易数量 {position_size_in_contracts} 小于最小交易量 {min_amount}")
            return None
        
        if order_value < min_cost:
            logger.error(f"订单金额 {order_value} 小于最小订单金额 {min_cost}")
            return None
        
        # 初始名义金额计算
        notional = position_size_in_contracts * trigger_price
        logger.debug(f"notional={notional}")
        
        return position_size_in_contracts
    except Exception as e:
        logger.error(f"计算开仓数量时报错: {e}")
        raise

async def get_market_min_order(symbol):
    """获取交易对的最小交易量和最小交易金额"""
    try:
        markets = await exchange.fetch_markets()
        
        for market in markets:
            if market['symbol'] == symbol:
                min_amount = market['limits']['amount']['min']  # 获取最小交易量
                min_cost = market['limits']['cost']['min']  # 获取最小订单金额
                contract_type = 'USDT' if 'USDT' in market['quote'] else 'COIN'  # 判断是 U 本位还是币本位合约
                logger.debug(f"交易所要求的min_amount: {min_amount}")
                logger.debug(f"交易所要求的min_cost: {min_cost}")
                logger.debug(f"当前本位类型contract_type: {contract_type}")
                return min_amount, min_cost, contract_type
        return None, None, None
    except Exception as e:
        logger.error(f"获取交易对报错: {e}")
        raise

async def check_positions(symbol):
    """检查持仓, 可能会在5分钟内频繁调用吧?"""
    global global_state
    # 重置订单状态
    global_state.has_long_position = global_state.has_short_position = global_state.has_other_position = global_state.has_position = False
    global_state.long_contracts = global_state.short_contracts = 0.0

    try:
        positions = await exchange.fetch_positions()
        if len(positions) == 0:
            logger.info(f"  当前没有任何持仓")
            return
        else:
            has_position = True
            logger.info(f"  当前有持仓：")
            for position in positions:
                if position['symbol'] == symbol:
                    if position['side'] == 'long':
                        has_long_position = True
                        logger.info(f"  当前币种有多头持仓：{position}")
                        long_contracts = float(position['contracts'])
                    else:
                        has_short_position = True
                        logger.info(f"  当前币种有空头持仓：{position}")
                        short_contracts = float(position['contracts'])
                else:
                    logger.info(f"  当前有其他币种的持仓：{position}")
                    has_other_position = True
    except Exception as e:
        logger.error(f"  获取持仓报错: {e}")
        raise

async def check_order(symbol):
    """检查挂单, 可能会在5分钟内频繁调用吧?"""
    global global_state
    # 重置挂单状态
    global_state.has_open_long = global_state.has_open_short = global_state.has_close_long = global_state.has_close_short = global_state.has_order = global_state.has_other_order = False

    try:
        # 获取未成交订单
        open_orders = await exchange.fetch_open_orders(symbol)
        if len(open_orders) == 0:
            logger.info(f"  当前没有任何挂单")
            return
        else:
            global_state.has_order = True
            logger.info(f"  当前有挂单：")
            for order in open_orders:
                if order['symbol'] == symbol:
                    side = order['side']
                    reduce_only = order.get('reduceOnly', False)
                    # 判断挂单状态
                    if side == 'buy':
                        if reduce_only:
                            global_state.has_close_short = True  # 平空挂单
                            logger.info(f"  当前币种有平空挂单：{order}")
                        else:
                            global_state.has_open_long = True  # 开多挂单
                            logger.info(f"  当前币种有开多挂单：{order}")
                    elif side == 'sell':
                        if reduce_only:
                            global_state.has_close_long = True  # 平多挂单
                            logger.info(f"  当前币种有平多挂单：{order}")
                        else:
                            global_state.has_open_short = True  # 开空挂单
                            logger.info(f"  当前币种有开空挂单：{order}")
                else:
                    logger.info(f"  当前有其他币种的挂单：{order}")
                    global_state.has_other_order = True
    except Exception as e:
        logger.error(f"  获取订单报错: {e}")
        raise

async def retry_create_order(symbol, order_type, side, position_side, amount, trigger_price, retries=3):
    for attempt in range(retries):
        try:
            return await create_trigger_order(symbol, order_type, side, position_side, amount, trigger_price)
        except ccxt.NetworkError as e:
            logger.error(f"网络错误，重试 {attempt + 1}/{retries} 次")
            await asyncio.sleep(2)  # 等待 2 秒再重试
        except Exception as e:
            logger.error(f"创建订单报错: {e}")
            raise
        if attempt + 1 == retries:
            raise CustomError("订单创建失败，已重试多次")


async def create_trigger_order(symbol, order_type, side, position_side, amount, trigger_price):
    """
    创建触发订单，用于开多、开空、平多或平空。

    :param symbol: 交易对，例如 'BTC/USDT'
    :param order_type: 订单类型，例如 'market' 或 'limit'
    :param side: 订单方向，'buy' 表示买入，'sell' 表示卖出
    :param position_side: 持仓方向，'LONG' 或 'SHORT'
    :param amount: 下单数量
    :param trigger_price: 触发价格，仅适用于条件单
    """
    params = {
        'positionSide': position_side
    }

    # 根据操作类型来决定 reduceOnly 的值
    if side == 'buy' and position_side == 'SHORT':  # 平空
        params['reduceOnly'] = True
    elif side == 'sell' and position_side == 'LONG':  # 平多
        params['reduceOnly'] = True
    
    if trigger_price:
        params['triggerPrice'] = trigger_price  # 设置触发价格

    # 日志记录订单详情
    logger.info(f"       ✅ 准备创建订单: symbol={symbol}, type={order_type}, side={side}, amount={amount}, "
            f"params={params}")

    # 创建订单
    order = await exchange.create_order(
        symbol=symbol,
        type=order_type,
        side=side,
        amount=amount,
        price=None,  # 市价单不需要设置价格
        params=params
    )
    logger.info(f"       ✅ 订单创建成功: {order}")
    # return order


async def cancel_all_orders(symbol):
    """撤销挂单"""
    try:
        open_orders = await exchange.fetch_open_orders(symbol)
        if open_orders:
            logger.info(f"       ✅ 发现{len(open_orders)}个挂单, 开始撤销...")
            for order in open_orders:
                await exchange.cancel_order(order['id'], symbol)
                logger.info(f"       ✅ ...撤销挂单成功: {order['id']}")
        else:
            logger.debug("       ✅ ...没有找到挂单, 无需撤销")
    except Exception as e:
        logger.error(f"       ❌ 撤销挂单时发生错误: {e}")
        raise

# 邮件通知配置
# def send_email(subject, body):
#     """发送邮件通知"""
#     sender_email = "<EMAIL>"
#     receiver_email = "<EMAIL>"
#     password = "your_email_password"
#     msg = MIMEMultipart()
#     msg['From'] = sender_email
#     msg['To'] = receiver_email
#     msg['Subject'] = subject
#     msg.attach(MIMEText(body, 'plain'))
#     try:
#         server = smtplib.SMTP('smtp.example.com', 587)
#         server.starttls()
#         server.login(sender_email, password)
#         server.sendmail(sender_email, receiver_email, msg.as_string())
#         server.quit()
#         logger.debug("邮件发送成功")
#     except Exception as e:
#         logger.error(f"发送邮件时报错: {e}")
#         raise

async def is_dual_side_position_mode():
    """当前账户是否为双向持仓模式"""
    try:
        # 双向持仓模式（Hedge Mode）
        # 单向持仓模式（One-Way Mode）
        response = await exchange.fapiprivate_get_positionside_dual()
        logger.info(f"当前的持仓模式: {response}")
        if response['dualSidePosition']:
            return True
        else:
            return False
    except Exception as e:
        logger.error(f"获取持仓模式报错: {e}")
        raise
    
async def set_to_dual_side_position(exchange):
    try:
        response = await exchange.fapiPrivatePostPositionSideDual({'dualSidePosition': 'true'})
        if response.get("code") == "200":
            logger.info("成功设置为双向持仓模式")
        else:
            logger.error(f"设置为双向持仓模式时, 交易所返回: {response}")
            raise CustomError(ErrorCode.SET_DUAL_FAIL)
    except Exception as e:
        logger.error(f"设置持仓模式报错: {e}")
        raise

# 设置环境
async def setup_env(stepX):

    logger.info(f"{stepX}、设置环境...")
    
    # 1、检查余额
    logger.info(f"{stepX}-1、检查余额")
    if not await check_balance_usdt():
        raise CustomError(ErrorCode.INSUFFICIENT_AMOUNT)

    # 2、设置持仓模式
    logger.info(f"{stepX}-2、设置持仓模式")
    is_dual = await is_dual_side_position_mode()
    if not is_dual:
        # 单向持仓模式时，获取持仓状态和挂单状态
        await check_positions(trade_symbol)
        await check_order(trade_symbol)
        if global_state.has_position or global_state.has_order:
            # 如果有持仓或者有挂单、且是单向持仓模式，无法直接修改为双向持仓模式，不满足环境要求
            raise CustomError(ErrorCode.NOT_SET_DUAL)
        else:
            # 如果没有持仓、也没有挂单、且是单向持仓模式，则设置为双向持仓模式
            logger.info("单向持仓模式下没有持仓、也没有挂单，则设置为双向持仓模式")
            await set_to_dual_side_position(exchange)
    else:
        logger.info("已经是双向持仓模式，无需设置")
        
    # 3、设置杠杆
    try:
        logger.info(f"{stepX}-3、设置杠杆")
        await exchange.set_leverage(leverage, trade_symbol)
        logger.info(f"杠杆设置成功: {leverage}倍")
    except Exception as e:
        logger.warning(f'设置杠杆失败: {e}')
        logger.warning(f'跳过设置, 后续会使用原有杠杆进行下单')

    # 4、可能的其他设置

    logger.info("...设置完成")

# 初始化历史数据表
async def fetch_initial_ohlcv(stepX):
    # logger.info(f"本地时间:   {get_local_time().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"{stepX}、初始化历史K线数据表...")
    global global_state
    try:
        # logger.info(f"{stepX}-1、获取最近历史K线的OHLCV并转为内存数据表")
        ohlcv = await exchange.fetch_ohlcv(trade_symbol, timeframe, limit=99)
        global_state.ohlcv_data = pd.DataFrame(ohlcv, columns=["timestamp", "open", "high", "low", "close", "volume"])
        if len(global_state.ohlcv_data) < ema_period:
            raise CustomError(ErrorCode.OHLCV_NOT_ENOUGH)
    
        # Binance API 返回的 UNIX 毫秒时间戳, 默认是 UTC 时间
        # global_state.ohlcv_data['timestamp'] = pd.to_datetime(global_state.ohlcv_data['timestamp'], unit='ms')
        global_state.ohlcv_data['timestamp'] = pd.to_datetime(global_state.ohlcv_data['timestamp'], unit='ms') + pd.Timedelta(hours=8)  # +8小时, 就是北京时间了
        # logger.info(f"{stepX}-2、计算每根K线的EMA{ema_period}")
        global_state.ohlcv_data[EMA_COLUMN] = global_state.ohlcv_data['close'].ewm(span=ema_period).mean().round(8)
        # logger.info(global_state.ohlcv_data.tail())
        logger.debug(f"历史K线数据表如下:\n" + format_dataframe_with_tabulate(global_state.ohlcv_data.tail(), indent=32))

        logger.info("...初始化完成")
    except Exception as e:
        logger.error(f"初始化历史数据报错: {e}")
        raise

# 使用 watch_ohlcv 实现实时更新
async def watch_ohlcv_update(stepX):
    global global_state
    retries = retries_count
    logger.info(f"{stepX}、处理实时增量数据...")
    while retries > 0:
        try:
            
            logger.debug(f"Start:   {get_local_time().strftime('%Y-%m-%d %H:%M:%S')}")
            logger.debug(f"{stepX}-1、开始获取ws推送来的当前这根{timeframe}K线数据...")
            ohlcv = await exchange.watch_ohlcv(trade_symbol, timeframe)
            logger.debug(ohlcv[-1])
            logger.debug(f"...获取完成")
            # ohlcv_temp = pd.DataFrame(ohlcv, columns=["timestamp", "open", "high", "low", "close", "volume"])
            # # +8小时, 就是北京时间了
            # ohlcv_temp['timestamp'] = pd.to_datetime(ohlcv_temp['timestamp'], unit='ms') + pd.Timedelta(hours=8)
            # ohlcv_temp[EMA_COLUMN] = ohlcv_temp['close'].ewm(span=ema_period).mean().round(8)
            # logger.debug(ohlcv_temp.tail())

            logger.debug(f"{stepX}-2、把当前这根{timeframe}K线更新(或新增)到历史K线数据表...")
            latest_kline = ohlcv[-1]
            latest_row = {
                # "timestamp": pd.to_datetime(latest_kline[0], unit='ms'),
                "timestamp": pd.to_datetime(latest_kline[0], unit='ms') + pd.Timedelta(hours=8),  # +8小时, 就是北京时间了
                "open": latest_kline[1],
                "high": latest_kline[2],
                "low": latest_kline[3],
                "close": latest_kline[4],
                "volume": latest_kline[5],
            }
            # 更新历史K线数据表中的最后一条数据 或 新增一条数据
            if global_state.ohlcv_data.iloc[-1]['timestamp'] == latest_row['timestamp']:
                # 更新最后一条数据
                global_state.ohlcv_data.iloc[-1] = latest_row
                is_update = True
            else:
                # 新增一条数据
                global_state.ohlcv_data.loc[len(global_state.ohlcv_data)] = latest_row
                is_update = False

            # 重新计算全部的EMA -- 浪费性能
            # global_state.ohlcv_data[EMA_COLUMN] = global_state.ohlcv_data['close'].ewm(span=ema_period).mean().round(8)
            # logger.debug(f"历史K线数据表如下:\n" + format_dataframe_with_tabulate(global_state.ohlcv_data.tail(), indent=32))
            # if(is_update):
            #     logger.debug(f"...更新完成")
            # else:
            #     logger.debug(f"...新增完成")

            # 只计算当前这根K线的EMA -- 增量计算，只计算最后一行
            # 获取上一行(倒数第二行)的EMA
            previous_ema = global_state.ohlcv_data.iloc[-2][EMA_COLUMN]
            latest_close = global_state.ohlcv_data.iloc[-1]['close']
            alpha = 2 / (ema_period + 1)
            latest_ema = (latest_close * alpha) + (previous_ema * (1 - alpha))
            global_state.ohlcv_data.at[len(global_state.ohlcv_data) - 1, EMA_COLUMN] = round(latest_ema,8)

            logger.debug(f"历史K线数据表如下:\n" + format_dataframe_with_tabulate(global_state.ohlcv_data.tail(), indent=32))
            if is_update:
                logger.debug(f"...更新完成")
            else:
                logger.debug(f"...新增完成")

            logger.debug(f"{stepX}-3、开始执行交易...")
            await execute_trading(f"{stepX}-3", global_state.ohlcv_data)
            logger.debug("...执行完成")
            logger.debug(f"End:   {get_local_time().strftime('%Y-%m-%d %H:%M:%S')}")
            logger.debug(f"--------------------------------------------------------------------------------------------------------")
            
            # 最后把重试次数进行重置
            if(retries != retries_count):
                retries = retries_count

        except RequestTimeout as e:
            retries -= 1
            logger.error(f"连接超时, 重试剩余次数: {retries}")
            logger.error(f"错误详情: {str(e)}")
            await asyncio.sleep(2)  # 等待 2 秒后重试
        except NetworkError as e:
            logger.error(f"网络错误: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"处理实时增量数据报错: {e}")
            raise
        if retries == 0:
            logger.error(f"多次重试后仍失败, {current_file_name} 退出, 请检查网络! ")
            break
    logger.info("...处理完成")

# 使用 watch_trades 动态构建K线，还未测试，存疑
# async def watch_trades_update(stepX):
#     global ohlcv_data
#     trades_buffer = []
#     retries = retries_count
#     while retries > 0:
#         try:
#             trade = await exchange.watch_trades(trade_symbol)
#             trades_buffer.append(trade)
#             new_ohlcv = exchange.build_ohlcv(trades_buffer, timeframe, since=None, limit=99)
#             if new_ohlcv:
#                 ohlcv_data = pd.DataFrame(new_ohlcv, columns=["timestamp", "open", "high", "low", "close", "volume"])
#                 ohlcv_data['timestamp'] = pd.to_datetime(ohlcv_data['timestamp'], unit='ms')
#                 ohlcv_data[EMA_COLUMN] = ohlcv_data['close'].ewm(span=ema_period).mean()
#                 logger.debug(f"{stepX}-1、构建的实时K线:")
#                 logger.debug(f"历史K线数据表如下:\n" + format_dataframe_with_tabulate(ohlcv_data.tail(), indent=32))
    
#                 # 执行交易
#                 await execute_trading(ohlcv_data)

#                 # 最后把重试次数进行重置
#                 if(retries != retries_count):
#                     retries = retries_count
#         except ccxt.RequestTimeout as e:
#             retries -= 1
#             logger.debug(f"连接超时, 重试剩余次数: {retries}")
#             logger.debug(f"错误详情: {str(e)}")
#             await asyncio.sleep(2)  # 等待 2 秒后重试
#         except ccxt.NetworkError as e:
#             logger.debug("网络错误, 检查您的网络连接")
#             logger.debug(f"错误详情: {str(e)}")
#             break
#         except Exception as e:
#             logger.error(f"处理实时增量数据报错: {e}")
#             raise
#         if retries == 0:
#             logger.debug(f"多次重试后仍失败, {current_file_name} 退出, 请检查网络! ")
#             break


# 交易逻辑
async def execute_trading(stepY,df):
    
    global global_state
    
    # 检查 last_low 和 last_high 的值是否有变化
    if (global_state.last_status['last_low'] is None or global_state.last_status['last_high'] is None or
        global_state.last_status['last_low'] != df.iloc[-1]['low'] or global_state.last_status['last_high'] != df.iloc[-1]['high']):
        # 更新 last_low 和 last_high
        global_state.last_status['last_low'] = df.iloc[-1]['low']
        global_state.last_status['last_high'] = df.iloc[-1]['high']
        logger.debug(f"{stepY}-1、这根K线high和low相较上次有变化, 新值更新到last_status: last_high={global_state.last_status['last_high']}, last_low={global_state.last_status['last_low']}")
    else:
        logger.debug(f"{stepY}-1、这根K线high和low相较上次均未改变, 无需操作, 等待下次ws数据推送")
        return
    
    # 找起始K线
    start_index = None
    for i in range(len(df) - 1, -1, -1):
        if df.iloc[i]['low'] <= df.iloc[i][EMA_COLUMN] <= df.iloc[i]['high']:
            logger.debug(f"{stepY}-2、找到了起始K线, 索引为: {i}")
            start_index = i
            break
    if start_index is None:
        logger.debug(f"{stepY}-2、未找到起始K线, 无需操作, 等待下次ws数据推送")
        return
    
    if global_state.last_status['last_start_index'] != start_index:
        # 说明起始K线变了, 原有的挂单是基于旧的起始K线的(也可能是之前手动挂的或其他程序挂的)，所以如果还未成交就需要全部撤销
        logger.debug(f"{stepY}-3、起始K线已经变了, 从{global_state.last_status['last_start_index']}变为{start_index}, 如果基于旧的起始K线创建的挂单(也可能是之前手动挂的或其他程序挂的)还未成交, 则需要全部撤销...")
        # 获取挂单状态
        await check_order(trade_symbol)
        # 撤销当前币种的所有挂单
        if global_state.has_close_short or global_state.has_open_long or global_state.has_close_long or global_state.has_open_short:
            await cancel_all_orders(trade_symbol)

        # 并且更新last_start_index, 不然下次进来还是旧数据
        global_state.last_status['last_start_index'] = start_index
        logger.debug(f"{stepY}-4、已经更新起始K线索引到last_status: last_start_index={global_state.last_status['last_start_index']}")
    else:
        logger.debug(f"{stepY}-3、起始K线未发生变化, 还是{start_index}")
        logger.debug(f"{stepY}-4、不需要更新last_status中的起始K线索引: last_start_index={global_state.last_status['last_start_index']}")

    # 交易逻辑
    # 检查连续entry_k_count根K线
    if start_index is not None and start_index + entry_k_count < len(df):  # 确保索引不会越界
        if(start_index + entry_k_count + 1 < len(df)):
            # 已经错过了最佳入场K
            logger.info(f"{stepY}-5、已经错过最佳入场K线{len(df)-(start_index + entry_k_count + 1)}根了, 不再入场, 等待下次ws数据推送")
            return

        if all(df.iloc[start_index + j]['low'] > df.iloc[start_index + j][EMA_COLUMN] for j in range(1, entry_k_count+1)):
            trigger_price = max(df.iloc[start_index + 1:start_index + entry_k_count+1]['high']) + tick_size_custom
            logger.info(f"{stepY}-5、连续{entry_k_count-1}根k线的最低价都在EMA{ema_period}均线上方, 准备开多: ")
            # 获取服务器时间
            server_time = await fetch_server_time()
            logger.info(f"       ✅ 服务器时间: {server_time.strftime('%Y-%m-%d %H:%M:%S')}")
            # 获取持仓状态
            await check_positions(trade_symbol)
            # 获取挂单状态
            await check_order(trade_symbol)
            if not global_state.has_long_position and not global_state.has_open_long:
                contracts = await enough_balance_to_order(df.iloc[-1]['close'],trigger_price)
                if contracts is None:
                    raise CustomError(ErrorCode.INSUFFICIENT_AMOUNT)
                # 持仓中没有多头仓位并且也没有挂单开多，则现在挂单开多
                await retry_create_order(trade_symbol, 'market', 'buy', 'LONG', contracts, trigger_price)
                logger.info(f"       ✅ 已挂单开多, 触发价: {trigger_price}")
                msg = f"✅ 多单触发条件满足(连续{entry_k_count-1}根k线的最低价都在EMA{ema_period}均线上方, 且持仓中没有多头仓位也没有挂多单), 已挂单开多, 触发价: {trigger_price}, 挂单时间: {server_time}"
                # send_email("交易通知", msg)
                # 发送Telegram消息
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, msg)
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")
            else:
                logger.info(f"       ✅ 持仓中已有多头仓位或已经挂了多单, 暂不开多了, 如果开多，其触发价: {trigger_price}")
            if global_state.has_short_position and not global_state.has_close_short:
                contracts = await enough_balance_to_order(df.iloc[-1]['close'],trigger_price)
                if contracts is None:
                    raise CustomError(ErrorCode.INSUFFICIENT_AMOUNT)
                # 持仓中有空单还未平仓并且也没有挂单平空, 则现在挂单平空
                await retry_create_order(trade_symbol, 'market', 'buy', 'SHORT', global_state.short_contracts, trigger_price)
                logger.info(f"       ✅ 已挂单平空, 触发价: {trigger_price}")
                msg = f"✅ 多单触发条件满足(连续{entry_k_count-1}根k线的最低价都在EMA{ema_period}均线上方, 且持仓中有空单还未平仓并且也没有挂单平空), 已挂单平空, 触发价: {trigger_price}, 挂单时间: {server_time}"
                # send_email("交易通知", msg)
                # 发送Telegram消息
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, msg)
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

        elif all(df.iloc[start_index + j]['high'] < df.iloc[start_index + j][EMA_COLUMN] for j in range(1, entry_k_count+1)):
            trigger_price = min(df.iloc[start_index + 1:start_index + entry_k_count+1]['low']) - tick_size_custom
            logger.info(f"{stepY}-5、连续{entry_k_count-1}根k线的最高价都在EMA{ema_period}均线下方, 准备开空: ")
            # 获取服务器时间
            server_time = await fetch_server_time()
            logger.info(f"       ✅ 服务器时间: {server_time.strftime('%Y-%m-%d %H:%M:%S')}")
            # 获取当前持仓状态
            await check_positions(trade_symbol)
            # 获取挂单状态
            await check_order(trade_symbol)
            if not global_state.has_short_position and not global_state.has_open_short:
                contracts = await enough_balance_to_order(df.iloc[-1]['close'],trigger_price)
                if contracts is None:
                    raise CustomError(ErrorCode.INSUFFICIENT_AMOUNT)
                # 持仓中没有空头仓位并且也没有挂单开空, 则现在挂单开空
                await retry_create_order(trade_symbol, 'market', 'sell', 'SHORT', contracts, trigger_price)
                logger.info(f"       ✅ 已挂单开空, 触发价: {trigger_price}")
                msg = f"✅ 空单触发条件满足(连续{entry_k_count-1}根k线的最高价都在EMA{ema_period}均线下方, 且当前持仓中没有空单), 已挂单开空, 触发价: {trigger_price}"
                # send_email("交易通知", msg)
                # 发送Telegram消息
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, msg)
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")
            else:
                logger.info(f"       ✅ 持仓中已有空头仓位或已经挂了空单, 暂不开空了, 如果开空，其触发价: {trigger_price}")
            if global_state.has_long_position and not global_state.has_close_long:
                contracts = await enough_balance_to_order(df.iloc[-1]['close'],trigger_price)
                if contracts is None:
                    raise CustomError(ErrorCode.INSUFFICIENT_AMOUNT)
                # 持仓中有多单还未平仓并且也没有挂单平多, 则现在挂单平多
                await retry_create_order(trade_symbol, 'market', 'sell', 'LONG', global_state.long_contracts, trigger_price)
                logger.info(f"       ✅ 已挂单平多, 触发价: {trigger_price}")
                msg = f"✅ 空单触发条件满足(连续{entry_k_count-1}根k线的最高价都在EMA{ema_period}均线下方, 且当前持仓中有多单还未平仓), 已挂单平多, 触发价: {trigger_price}"
                # send_email("交易通知", msg)
                # 发送Telegram消息
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, msg)
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")
    else:
        logger.info(f"{stepY}-5、暂未满足连续{entry_k_count-1}根K线的要求, 无需操作, 等待下次ws数据推送")


async def main():

    logger.info(f"{current_file_name} 开始！")
    # send_email(f"{current_file_name} 开始", "启动啦")
    # 发送Telegram消息
    try:
        await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"开始！")
    except Exception as e:
        logger.error(f"发送Telegram消息失败: {e}")



    trader_params = textwrap.dedent(f"""
    trade_symbol = {trade_symbol}
    timeframe = {timeframe}
    ema_period = {ema_period}
    entry_k_count = {entry_k_count}
    leverage = {leverage}
    position_size_ratio = {position_size_ratio}
    tick_size_custom = {tick_size_custom}""")

    # -- ccxt file path: {ccxt.__file__}
    # -- ccxt pro file path: {ccxtpro.__file__}
    # -- ccxt所有方法: {json.dumps(dir(ccxtpro.binance))}

    # 定义缩进的空格数量
    indent_spaces = " " * 44    # 44个空格，用字符串乘法代替手动输入

    message = f"""
                                请提供一个单独的账号只用于此程序进行自动化交易！以免造成混乱！
                                即: apiKey对应的账号, 只用于此程序对其进行操作, 如果被其他自动化程序或人为手动操作，可能造成错乱。
                                -- ccxt version:     {ccxt.__version__}
                                -- ccxt pro version: {ccxtpro.__version__}
                                -- https proxy: {exchange.https_proxy}
                                -- ws proxy:  {exchange.ws_proxy}
                                -- 交易参数: {textwrap.indent(trader_params, prefix=indent_spaces)}"""
    log_block(logger, "注意⚠️", message)

    logger.info(f"========================================================================================")

    while True:
        if global_state.exchange_is_down:
            logger.info(f"重新启动交易...")
            # send_email(f"{current_file_name} 重新启动交易", "重新启动交易")
            # 发送Telegram消息
            try:
                await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"重新启动交易...")
            except Exception as e:
                logger.error(f"发送Telegram消息失败: {e}")

        try:

            # 获取时间
            local_time = get_local_time()
            server_time = await fetch_server_time()
            logger.info(f"本地时间:   {local_time.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"服务器时间: {server_time.strftime('%Y-%m-%d %H:%M:%S')}")

            if global_state.exchange_is_down:
                global_state.exchange_is_down = False
                # 交易所服务正常了
                e_msg = f"✅ 交易所服务正常了"
                logger.info(f"{current_file_name} {e_msg}")
                # send_email(f"{current_file_name} ✅ 交易所服务正常了", {e_msg})
                # 发送Telegram消息（现在有限流保护）
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, e_msg)
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")

            logger.info(f"========================================================================================")

            # 0、设置环境...
            await setup_env(0)

            logger.info(f"========================================================================================")
            
            # 1、初始化历史K线数据表...
            await fetch_initial_ohlcv(1)

            logger.info(f"========================================================================================")
                    
            # 2、处理实时增量数据...
            await watch_ohlcv_update(2)  # 使用 watch_ohlcv
            # 另一种实时处理的方式
            # await watch_trades_update(2)  # 使用 watch_trades

        except (ExchangeNotAvailable, RequestTimeout, NetworkError) as e:
            logger.info(f"异常: {e}")
            global_state.exchange_is_down = True
            # 交易所服务宕机或不可用, 一会继续重试
            e_msg = f"❎ 交易所服务异常 ({type(e).__name__}), 本交易无法继续执行，{EXCHANGE_DOWN_WAITING} 后重新启动交易..."
            logger.info(f"{current_file_name} {e_msg}")
            # send_email(f"{current_file_name} ❎ 交易所服务异常 ({type(e).__name__})", e_msg)
            # 使用防重复发送工具
            await handle_exchange_error(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, e_msg)
            # 从本地缓存中读取出来 如果还有未成交的订单或未平仓的合约。（所以要通过watch_orders实时监听订单的状态、[难道也是通过watch_orders？还是通过watch_my_trades？]获取未平仓的合约状态？，然后存到本地缓存中）
            if("本地缓存中有未成交的订单或未平仓的合约"):
                # 分别读取详细信息并封装成便于telegram展示的样式, 类似于：
                    # 1未成交的订单，继续挂着 还是 手动撤销？当前有3单：btc多单、eth空单、doge多单
                    # 2未平仓的合约，继续持有 还是 手动平仓？ 当前有2单：btc空单、eth多单
                open_orders = ""
                open_contracts = ""
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"❗️ 鉴于本任务还有未成交的订单或未平仓的合约, 建议去交易所app自行决定如何处理如下订单:\n{open_orders}\n{open_contracts}")
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")
            # 重新启动交易之前需要重置所有全局变量的状态，以防还保留之前的状态导致逻辑错乱
            reset_global_state() 
            wait_time = parse_interval(EXCHANGE_DOWN_WAITING)
            await asyncio.sleep(wait_time)
        except CustomError as e:
            global_state.exchange_is_down = False
            reason = f"{str(e)}"
        except asyncio.CancelledError:
            global_state.exchange_is_down = False
            # 任务被取消，输出换行符避免与^C共行
            print_ctrl_c_newline()
            reason = f"主动取消"
        except Exception as e:
            global_state.exchange_is_down = False
            logger.error(f"报错: {traceback.format_exc()}")
            reason = f"报错: {repr(e)}"
        finally:
            if not global_state.exchange_is_down:
                # 显示 `reason` 的默认值，以防止未设置导致的问题
                if 'reason' not in locals():  # 判断 reason 是否被赋值
                    reason = "执行结束"
                # send_email(f"{current_file_name} 停止", f"{reason}")
                # 发送Telegram消息
                try:
                    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"停止！原因: {reason}")
                except Exception as e:
                    logger.error(f"发送Telegram消息失败: {e}")
                # exchange由全局管理，无需在此处关闭
                logger.info(f"{current_file_name} 停止！原因: {reason}")
                return {"task_name":current_file_name, "task_result":reason}

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        # 任务被取消，输出换行符避免与^C共行
        print_ctrl_c_newline()
        logger.info(f"主动取消")
    finally:
        # 程序级清理（关闭exchange）- 只在独立运行时执行
        from utils.exchange_manager import close_global_exchange
        try:
            asyncio.run(close_global_exchange())
            logger.info("✅ Exchange连接已关闭")
        except Exception as e:
            logger.error(f"Exchange关闭时出错: {e}")