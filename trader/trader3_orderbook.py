"""交易：吃盘口：获取订单簿数据，通过小额来成交买一或卖一，压低或推高成交价，推动行情向有利于我持仓的方向"""
# 前提是要已经有持仓了，再根据持仓方向执行此交易
# 暂未包含重试机制

import sys
import asyncio
import pandas as pd
import traceback
from dotenv import load_dotenv
import os

# 添加项目根目录到 sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.utils_logger import setup_logger
from errors import CustomError
from utils.utils_telegram import send_telegram_message
from constants import TASK_DIVIDER
from utils.exchange_wrapper import create_exchange_wrapper
from utils.utils_common import print_ctrl_c_newline

# 初始化日志记录器
current_file_name = os.path.splitext(os.path.basename(__file__))[0]
log_name = f"{current_file_name}_logger"
log_file_name = f"{current_file_name}.log"
logger = setup_logger(log_name=log_name,log_file=log_file_name)

# 加载.env文件, 读取bot_token和chat_id
load_dotenv()
TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN_TRADER")
TELEGRAM_CHAT_ID = os.getenv("TELEGRAM_CHAT_ID")

# 使用全局exchange包装器
exchange = create_exchange_wrapper(current_file_name, logger)

# todo: 收益控制（主动止盈：固定收益率止盈or固定金额止盈）
# todo: 风险控制（被动止损：固定收益率止损or固定金额止损）
# todo: 心跳监控（单独开一个心跳监控线程？）

# 交易参数，可根据需要进行修改
trade_symbol = "BTC/USDT:USDT"
timeframe = "5m"
ema_period = 20
entry_k_count = 4  # 起始K之后第几跟K开始入场，值要>=2（也就是起始K之后要等 entry_k_count-1 根K线确认才能入场）
leverage = 5   # 初始杠杆倍数
position_size_ratio = 0.5  # 50%仓位，每次开仓比例
tick_size_custom = 0.2  # 需要>=价格变动的最小单位，因为流动性的关系，建议设置的比tick_size大一点，就trade_symbol="BTC/USDT:USDT"来说，建议设置为0.5、1、6、8、12
retries_count = 3  # 请求超时后的重试次数

#  ******** 👇下面的数据都不要修改👇 ********

# 定义全局变量: 仓位状态
has_long_position = False
has_short_position = False
has_position = False        # 是否有持仓（不管是不是本币种，其他币种也算）
has_other_position = False  # 预留，暂时用不到，不用关心此值
long_contracts = 0.0       # 多头持仓的合约数量，单位是币(不是usdt)
short_contracts = 0.0      # 空头持仓的合约数量，单位是币(不是usdt)

# 定义全局变量: 挂单状态
has_open_long = False   # 是否有挂单开多
has_open_short = False  # 是否有挂单开空
has_close_long = False  # 是否有挂单平多
has_close_short = False # 是否有挂单平空
has_order = False       # 是否有挂单（不管是不是本币种，其他币种也算）
has_other_order = False # 预留，暂时用不到，不用关心此值

# 定义全局变量: 存放历史K线数据
ohlcv_data = pd.DataFrame()

# 定义全局变量: 上次ws数据的一些状态，用于与这次ws数据进行比较
last_status = {
    'last_low': None,               # 上次从ws获取到的K线的最低价
    'last_high': None,              # 上次从ws获取到的K线的最高价
    'last_start_index': None,       # 上次计算得到的起始K线索引
}

# 定义常量: 
EMA_COLUMN = f"ema{ema_period}"

#  ******** 👆上面的数据都不要修改👆 ********

async def main():

    logger.info(f"{current_file_name} 开始！")
    # send_email(f"{current_file_name}开始", "启动啦")
    # 发送Telegram消息
    await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"开始！")



    try:

        print("todo")

        # while True:
        #     print("watch_order_book:")
        #     print(await exchange.watch_order_book('AVAX/USDT:USDT',5))
            
        # ticker = await exchange.watch_ticker(trade_symbol)
        # trade_params = {
        #     'name': 'aggTrade',
        # }
        # trades = await exchange.watch_trades(trade_symbol,params=trade_params)
        
        # print("ticker:",ticker)
        # print("trades:",trades)

    except CustomError as e:
        reason = f"{str(e)}"
    except asyncio.CancelledError:
        # 任务被取消
        print()
        reason = f"主动取消"
    except Exception as e:
        logger.error(f"报错: {traceback.format_exc()}")
        reason = f"报错: {repr(e)}"
    finally:
        # 显示 `reason` 的默认值，以防止未设置导致的问题
        if 'reason' not in locals():  # 判断 reason 是否被赋值
            reason = "执行结束"
        # send_email(f"{current_file_name} 停止", f"{reason}")
        # 发送Telegram消息
        await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"停止！原因: {reason}")
        # exchange由全局管理，无需在此处关闭
        logger.info(f"{current_file_name} 停止！原因: {reason}")
        return {"task_name":current_file_name, "task_result":reason}

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        # 任务被取消，输出换行符避免与^C共行
        print_ctrl_c_newline()
        logger.info(f"主动取消")
    finally:
        # 程序级清理（关闭exchange）- 只在独立运行时执行
        from utils.exchange_manager import close_global_exchange
        try:
            asyncio.run(close_global_exchange())
            logger.info("✅ Exchange连接已关闭")
        except Exception as e:
            logger.error(f"Exchange关闭时出错: {e}")