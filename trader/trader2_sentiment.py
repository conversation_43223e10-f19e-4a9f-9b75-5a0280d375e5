"""交易：抓情绪：目前只是画图来展示多空比和多空量，暂未加入交易的逻辑"""
# 初衷是想通过多空比和成交量来确定当前市场情绪，并进行开仓和平仓，暂只实现绘制和动态刷新图来展示多空比和多空量

import sys
import asyncio
import signal
import pandas as pd
import smtplib
import textwrap
import traceback
import math
# import json
from dotenv import load_dotenv
import os
from tabulate import tabulate
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime, timezone, timedelta
from matplotlib.animation import FuncAnimation
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from tqdm import tqdm
import time
import mplcursors

# 添加项目根目录到 sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.utils_logger import setup_logger
from utils.utils_logger import log_block
from utils.errors import CustomError, ErrorCode
from utils.utils_telegram import send_telegram_message
from utils.constants import TASK_DIVIDER, EXCHANGE_DOWN_WAITING
from utils.utils_common import format_volume, parse_interval, get_sleep_time, format_time

# 初始化日志记录器
current_file_name = os.path.splitext(os.path.basename(__file__))[0]
log_name = f"{current_file_name}_logger"
log_file_name = f"{current_file_name}.log"
logger = setup_logger(log_name=log_name,log_file=log_file_name)

# 加载.env文件, 读取apiKey和secret 【已经添加.env到.gitignore中，所以不会被提交到git中，也就不会泄露】
load_dotenv()
API_KEY = os.getenv("API_KEY")
API_SECRET = os.getenv("API_SECRET")

# 配置Binance交易所
# exchange = ccxtpro.binance({
#     'apiKey': API_KEY,
#     'secret': API_SECRET,
#     'enableRateLimit': True,
#     'http_proxy': 'http://127.0.0.1:7897',
#     'ws_proxy':' http://127.0.0.1:7897',
# })

# 使用全局exchange
from utils.exchange_wrapper import create_exchange_wrapper
from utils.exchange_manager import global_exchange_manager, close_global_exchange

# 创建exchange包装器
exchange = create_exchange_wrapper("trader2_sentiment", logger)

# todo: 收益控制（主动止盈：固定收益率止盈or固定金额止盈）
# todo: 风险控制（被动止损：固定收益率止损or固定金额止损）
# todo: 心跳监控（单独开一个心跳监控线程？）

# 交易参数
trade_symbol = "BTCUSDT"
timeframe = "5m"
ema_period = 20
entry_k_count = 4  # 起始K之后第几跟K开始入场，值要>=2（也就是起始K之后要等 entry_k_count-1 根K线确认才能入场）
leverage = 5   # 初始杠杆倍数
position_size_ratio = 0.5  # 50%仓位，每次开仓比例
tick_size_custom = 0.2  # 需要>=价格变动的最小单位，因为流动性的关系，建议设置的比tick_size大一点，就trade_symbol="BTC/USDT:USDT"来说，建议设置为0.5、1、6、8、12
retries_count = 3  # 请求超时后的重试次数


# def fetch_open_interest(trade_symbol):
#     """获取未平仓合约数"""
#     try:
#         oi = exchange.fetch_open_interest(trade_symbol)
#         print(oi)
#     except Exception as e:
#         logger.error(f"获取未平仓合约数报错: {e}")
#         raise


# fetch_open_interest(trade_symbol)


# 创建双子图
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10), sharex=True, gridspec_kw={'height_ratios': [2, 2]})

# 定义更新函数
async def update(frame=None):

    params = {
        "symbol": trade_symbol,
        "period": timeframe,   # 时间间隔，如 "1h", "4h", "1d"
        "limit": 200,           # 返回的记录数，默认为 30，最大为 500
    }

    data = await exchange.fapidata_get_takerlongshortratio(params)
    # 将时间戳转换为北京时间
    for entry in data:
        # 将时间戳从字符串转为整数（毫秒）
        timestamp_ms = int(entry['timestamp'])
        # 转换为 UTC 时间
        utc_time = datetime.fromtimestamp(timestamp_ms / 1000, tz=timezone.utc)
        # 加上 8 小时偏移量，得到北京时间
        beijing_time = utc_time + timedelta(hours=8)
        # 格式化为字符串，添加到数据中
        entry['timestamp'] = beijing_time.strftime('%Y-%m-%d %H:%M:%S')

    # 输出结果
    for entry in data:
        print(entry)


    # 转换为 DataFrame
    df = pd.DataFrame(data)

    # 处理数据
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df['buySellRatio'] = df['buySellRatio'].astype(float)
    df['sellVol'] = df['sellVol'].astype(float)
    df['buyVol'] = df['buyVol'].astype(float)

    # 设置柱状图宽度和偏移量
    bar_width = 0.3
    timestamps = np.arange(len(df))

    # 清空子图
    ax1.clear()
    ax2.clear()

    # 绘制 buySellRatio 的折线图（在上半部分）
    ax1.plot(timestamps, df['buySellRatio'], marker='o', label='BuySellRatio', color='blue', linewidth=2)
    ax1.axhline(1, color='gray', linestyle='--', linewidth=1.3, label='Neutral (1)')
    ax1.axhline(0.4, color='red', linestyle='--', linewidth=1.8, label='Threshold (0.4)')
    ax1.axhline(1.6, color='green', linestyle='--', linewidth=1.8, label='Threshold (1.6)')

    # ax1.axhspan(0, 0.4, color='red', alpha=0.3)  # 在0到0.4之间填充淡红色
    # ax1.axhspan(1.6, ax1.get_ylim()[0], color='green', alpha=0.3)  

    ax1.axhspan(ax1.get_ylim()[0], 0.5, color='red', alpha=0.3)  # 在0到0.4之间填充淡红色
    ax1.axhspan(1.5, ax1.get_ylim()[1], color='green', alpha=0.3)  # 大于1.6填充淡绿色

    # 设置 y 轴倒置
    ax1.invert_yaxis()

    # 设置 ax1 的 y 轴标签和刻度到右侧
    ax1.yaxis.set_label_position("right")
    ax1.yaxis.tick_right()

    # 设置 ax1 属性
    ax1.set_title('BuySellRatio and Volume Over Time', fontsize=16)
    ax1.set_ylabel('BuySellRatio', fontsize=12, color='blue')
    ax1.legend(fontsize='small', bbox_to_anchor=(1, 1.15), ncol=1)
    ax1.grid(alpha=0.3)

    # 绘制 sellVol 和 buyVol 的柱状图（在下半部分）
    ax2.bar(timestamps - bar_width / 2, df['sellVol'], width=bar_width, label='Sell Volume', color='red', alpha=0.6)
    ax2.bar(timestamps + bar_width / 2, df['buyVol'], width=bar_width, label='Buy Volume', color='green', alpha=0.6)

    # 设置 ax2 属性
    ax2.set_ylabel('Volume', fontsize=12, color='blue')
    ax2.set_xlabel('Timestamp', fontsize=12)
    ax2.set_xticks(timestamps)
    ax2.set_xticklabels(df['timestamp'].dt.strftime('%H:%M'), rotation=45)
    ax2.legend(loc='upper right', fontsize='small')
    ax2.grid(alpha=0.3)

    # 设置 ax2 的 y 轴标签和刻度到右侧
    ax2.yaxis.set_label_position("right")
    ax2.yaxis.tick_right()

    # 添加mplcursor
    cursor = mplcursors.cursor([ax1, ax2], hover=True)
    cursor.connect(
        "add",
        lambda sel: sel.annotation.set_text(
            # 'Time: {}\nValue: {:.2f}'.format(sel.target[0], sel.target[1])
            # 'Time: {}\nValue: {:.2f}'.format(df['timestamp'][sel.target.index].strftime('%Y-%m-%d %H:%M:%S'), sel.target[1])
            'Time: {}\nValue: {:.2f}'.format(df['timestamp'].iloc[int(sel.target[0])].strftime('%Y-%m-%d %H:%M:%S'), sel.target[1])
            # 'Time: {}\nValue: {:.2f}'.format(df['timestamp'].iloc[sel.target.index].strftime('%Y-%m-%d %H:%M:%S'), sel.target[1])

        )
    )


# 创建同步包装函数用于FuncAnimation
def sync_update(frame=None):
    """同步包装函数，用于FuncAnimation调用"""
    import asyncio
    try:
        # 获取当前事件循环
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # 如果循环正在运行，创建任务
            task = asyncio.create_task(update(frame))
            return task
        else:
            # 如果循环未运行，直接运行
            return loop.run_until_complete(update(frame))
    except RuntimeError:
        # 如果没有事件循环，创建新的
        return asyncio.run(update(frame))

async def main():
    try:
        """异步主函数"""
        # 初始化图表内容
        await update()

        # 使用 FuncAnimation 动态更新
        ani = FuncAnimation(fig, sync_update, interval=120000, cache_frame_data=False)  # 每 2 分钟（120,000 毫秒）刷新一次

        # 显示动态图表
        plt.tight_layout()
        plt.show()
    except asyncio.CancelledError:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序异常: {e}")
    finally:
        # 任务级清理（不关闭exchange）
        print("✅ 任务清理完成")

# **启动程序**
if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("程序被用户中断")
    finally:
        try:
            asyncio.run(close_global_exchange())
            print("✅ Exchange连接已关闭")
        except Exception as e:
            print(f"Exchange关闭时出错: {e}")

