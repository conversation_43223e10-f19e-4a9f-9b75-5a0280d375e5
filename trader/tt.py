"""交易：抓趋势：通过站上或跌下均线来开仓和平仓"""
# 站上了，就开仓做多（如果有空单就平掉空单）
# 跌下了，就开仓做空（如果有多单就平掉多单）

### 此文件是AI对trader1_ema文件的优化建议，可参考，也可删除此文件

import sys
import asyncio
import ccxt
import ccxt.pro as ccxtpro
import pandas as pd
import smtplib
import textwrap
import traceback
import math
from dataclasses import dataclass
from dotenv import load_dotenv
import os
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from ccxt.base.errors import ExchangeNotAvailable, RequestTimeout, NetworkError

# 添加项目根目录到 sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.utils_logger import setup_logger, log_block
from utils.utils_telegram import send_telegram_message
from errors import CustomError, ErrorCode
from constants import TASK_DIVIDER, EXCHANGE_DOWN_WAITING
from utils.utils_common import (
    format_volume, parse_interval, get_sleep_time,
    format_time, format_dataframe_with_tabulate
)

# ---------------------------- 配置类 ----------------------------
@dataclass
class TraderConfig:
    """交易参数配置"""
    symbol: str = "BTC/USDT:USDT"
    timeframe: str = "5m"
    ema_period: int = 20
    entry_k_count: int = 4  # 起始K之后需要确认的K线数量（>=2）
    leverage: int = 5
    position_ratio: float = 0.5  # 仓位比例
    tick_size: float = 0.2  # 价格变动最小单位
    retries: int = 3  # 网络请求重试次数
    stop_loss: float = 0.02  # 止损比例
    max_retries: int = 5  # 最大重试次数
    retry_delay: int = 2  # 重试延迟(秒)

config = TraderConfig()

# ---------------------------- 状态管理 ----------------------------
@dataclass
class TradingState:
    """交易状态管理"""
    long_position: bool = False
    short_position: bool = False
    open_long_order: bool = False
    open_short_order: bool = False
    long_contracts: float = 0.0
    short_contracts: float = 0.0
    last_kline: dict = None
    ema_column: str = f"ema{config.ema_period}"
    
    def reset_positions(self):
        self.long_position = self.short_position = False
        self.long_contracts = self.short_contracts = 0.0
        
    def reset_orders(self):
        self.open_long_order = self.open_short_order = False

# ---------------------------- 初始化 ----------------------------
current_file = os.path.splitext(os.path.basename(__file__))[0]
logger = setup_logger(
    log_name=f"{current_file}_logger",
    log_file=f"{current_file}.log"
)

load_dotenv()
exchange = ccxtpro.binance({
    'apiKey': os.getenv("API_KEY"),
    'secret': os.getenv("API_SECRET"),
    'enableRateLimit': True,
    'https_proxy': 'http://127.0.0.1:7890',
    'ws_proxy': 'http://127.0.0.1:7890',
})

# ---------------------------- 核心功能 ----------------------------
class TrendTrader:
    def __init__(self, exchange, config):
        self.exchange = exchange
        self.config = config
        self.state = TradingState()
        self.ohlcv = pd.DataFrame()
        
    async def initialize(self):
        """初始化环境"""
        await self._setup_environment()
        await self._fetch_initial_data()
        
    async def _setup_environment(self):
        """环境配置"""
        if not await self.check_balance():
            raise CustomError(ErrorCode.INSUFFICIENT_AMOUNT)
            
        if not await self.ensure_dual_mode():
            raise CustomError(ErrorCode.NOT_SET_DUAL)
            
        await self.set_leverage()
        
    async def check_balance(self) -> bool:
        """检查账户余额"""
        balance = await self.exchange.fetch_balance({'type': 'future'})
        return balance['free']['USDT'] >= 10
        
    async def ensure_dual_mode(self) -> bool:
        """确保双向持仓模式"""
        if not await self.is_dual_mode():
            await self.set_dual_mode()
        return True
        
    async def set_leverage(self):
        """设置杠杆"""
        await self.exchange.set_leverage(self.config.leverage, self.config.symbol)
        
    async def run(self):
        """运行交易主循环"""
        while True:
            try:
                await self.process_realtime_data()
            except ExchangeNotAvailable:
                await self.handle_exchange_down()
                
    async def process_realtime_data(self):
        """处理实时数据"""
        ohlcv = await self.exchange.watch_ohlcv(self.config.symbol, self.config.timeframe)
        self.update_ohlcv(ohlcv[-1])
        await self.execute_trading()
        
    def update_ohlcv(self, kline):
        """更新K线数据"""
        # ...（保持原有数据更新逻辑，增加数据校验）
        
    async def execute_trading(self):
        """执行交易逻辑"""
        # ...（优化交易逻辑，增加信号生成模块）
        
    async def manage_orders(self, signal: str):
        """订单管理"""
        # ...（统一订单管理，增加风控检查）
        
    async def create_order(self, side: str, position_side: str):
        """创建订单（带风控检查）"""
        # ...（整合订单创建逻辑，增加前置检查）
        
# ---------------------------- 工具函数 ----------------------------
def retry(async_func):
    """重试装饰器"""
    async def wrapper(*args, **kwargs):
        for _ in range(config.max_retries):
            try:
                return await async_func(*args, **kwargs)
            except (NetworkError, RequestTimeout):
                await asyncio.sleep(config.retry_delay)
        raise CustomError(ErrorCode.NETWORK_ERROR)
    return wrapper
    
# ---------------------------- 主程序 ----------------------------
async def main():
    trader = TrendTrader(exchange, config)
    await trader.initialize()
    await trader.run()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("交易手动终止")
