#!/usr/bin/env python3
"""
配置重载测试脚本
测试配置文件的重载功能
"""

import sys
import os

# 添加项目根目录到 sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config_manager import config_manager, get_monitor_config, get_app_config
from utils.utils_logger import setup_logger

# 设置日志
logger = setup_logger("test_config_reload", "test_config_reload.log")

def test_config_reload():
    """测试配置重载功能"""
    logger.info("🧪 开始测试配置重载功能")
    
    # 1. 显示当前配置
    logger.info("📋 当前监控配置:")
    volume_threshold = get_monitor_config('monitor.volume.threshold_multiplier', 2.0)
    pressure_threshold = get_monitor_config('monitor.pressure.strong_buy_threshold', 70.0)
    logger.info(f"  成交量阈值倍数: {volume_threshold}")
    logger.info(f"  强买压阈值: {pressure_threshold}")
    
    enabled_monitors = get_app_config('app.tasks.enabled_monitors', [])
    logger.info(f"📋 当前启用的监控任务: {enabled_monitors}")
    
    # 2. 提示用户修改配置文件
    logger.info("🔧 请手动修改配置文件进行测试:")
    logger.info("  1. 修改 config/monitor_config.yaml 中的 threshold_multiplier 值")
    logger.info("  2. 修改 config/app_config.yaml 中的 enabled_monitors 列表")
    logger.info("  3. 按回车键继续测试重载功能...")
    
    input("按回车键继续...")
    
    # 3. 重载配置
    logger.info("🔄 重新加载监控配置...")
    config_manager.reload_config('monitor')
    
    logger.info("🔄 重新加载应用配置...")
    config_manager.reload_config('app')
    
    # 4. 显示重载后的配置
    logger.info("📋 重载后的监控配置:")
    new_volume_threshold = get_monitor_config('monitor.volume.threshold_multiplier', 2.0)
    new_pressure_threshold = get_monitor_config('monitor.pressure.strong_buy_threshold', 70.0)
    logger.info(f"  成交量阈值倍数: {new_volume_threshold}")
    logger.info(f"  强买压阈值: {new_pressure_threshold}")
    
    new_enabled_monitors = get_app_config('app.tasks.enabled_monitors', [])
    logger.info(f"📋 重载后的启用监控任务: {new_enabled_monitors}")
    
    # 5. 检查是否有变化
    if new_volume_threshold != volume_threshold:
        logger.info(f"✅ 成交量阈值已更新: {volume_threshold} -> {new_volume_threshold}")
    else:
        logger.info("ℹ️ 成交量阈值未变化")
        
    if new_enabled_monitors != enabled_monitors:
        logger.info(f"✅ 启用监控任务已更新: {enabled_monitors} -> {new_enabled_monitors}")
    else:
        logger.info("ℹ️ 启用监控任务未变化")
    
    # 6. 测试重载所有配置
    logger.info("🔄 重新加载所有配置...")
    config_manager.reload_all_configs()
    logger.info("✅ 所有配置重载完成")
    
    logger.info("🧪 配置重载测试完成")

def test_script_reload():
    """测试使用重载脚本"""
    logger.info("🧪 测试重载脚本功能")

    # 显示当前配置
    logger.info("📋 当前配置:")
    volume_threshold = get_monitor_config('monitor.volume.threshold_multiplier', 2.0)
    enabled_monitors = get_app_config('app.tasks.enabled_monitors', [])
    logger.info(f"  成交量阈值倍数: {volume_threshold}")
    logger.info(f"  启用的监控任务: {enabled_monitors}")

    logger.info("💡 你可以运行以下命令来重载配置:")
    logger.info("  python scripts/reload_config.py                    # 重载所有配置")
    logger.info("  python scripts/reload_config.py -t monitor         # 重载监控配置")
    logger.info("  python scripts/reload_config.py -t app             # 重载应用配置")
    logger.info("  python scripts/reload_config.py -t trader          # 重载交易配置")
    logger.info("  python scripts/reload_config.py -s                 # 显示当前配置")

    logger.info("🔧 或者通过Telegram发送重载命令:")
    logger.info("  /reload 或 重载配置                                # 重载所有配置")
    logger.info("  /reload_monitor 或 重载监控配置                     # 重载监控配置")
    logger.info("  /reload_app 或 重载应用配置                         # 重载应用配置")
    logger.info("  /reload_trader 或 重载交易配置                      # 重载交易配置")

def main():
    """主函数"""
    print("🧪 配置重载测试工具")
    print("=" * 50)
    print("1. 基本重载测试")
    print("2. 重载脚本使用说明")
    print("3. 退出")

    choice = input("请选择测试类型 (1-3): ").strip()

    if choice == "1":
        test_config_reload()
    elif choice == "2":
        test_script_reload()
    elif choice == "3":
        print("👋 再见！")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
