#!/usr/bin/env python3
"""测试配置管理器"""

import sys
import os

# 添加项目根目录到 sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config_manager import (
    config_manager,
    get_app_config,
    get_monitor_config,
    get_exchange_config,
    get_telegram_config,
    print_all_configs,
    reload_all_configs
)

def test_config_loading():
    """测试配置加载"""
    print("🧪 测试配置管理器...")
    
    print("\n📋 所有配置概览:")
    print_all_configs()

def test_specific_configs():
    """测试具体配置获取"""
    print("\n🔍 测试具体配置获取:")
    
    # 测试应用配置
    app_name = get_app_config('app.info.name', '未知应用')
    enabled_monitors = get_app_config('app.tasks.enabled_monitors', [])
    print(f"应用名称: {app_name}")
    print(f"启用的监控: {enabled_monitors}")
    
    # 测试监控配置
    volume_threshold = get_monitor_config('monitor.volume.threshold_multiplier', 2.0)
    pressure_threshold = get_monitor_config('monitor.pressure.strong_buy_threshold', 70.0)
    print(f"成交量阈值倍数: {volume_threshold}")
    print(f"强买压阈值: {pressure_threshold}%")
    
    # 测试交易所配置
    exchange_name = get_exchange_config('exchange.common.default_exchange', 'binance')
    rate_limit = get_exchange_config('exchange.binance.enable_rate_limit', True)
    print(f"默认交易所: {exchange_name}")
    print(f"启用限流: {rate_limit}")
    
    # 测试Telegram配置
    stop_commands = get_telegram_config('telegram.commands.stop', [])
    stop_reply = get_telegram_config('telegram.messages.stop_reply', '默认停止回复')
    print(f"停止命令数量: {len(stop_commands)}")
    print(f"停止回复: {stop_reply}")

def test_nested_access():
    """测试嵌套配置访问"""
    print("\n🎯 测试嵌套配置访问:")
    
    # 测试深层嵌套
    proxy_http = get_exchange_config('exchange.binance.proxy.http', '无代理')
    retry_count = get_exchange_config('exchange.binance.retry.max_retries', 3)
    
    print(f"HTTP代理: {proxy_http}")
    print(f"最大重试次数: {retry_count}")
    
    # 测试不存在的配置
    non_existent = get_app_config('app.non.existent.key', '默认值')
    print(f"不存在的配置: {non_existent}")

def test_config_reload():
    """测试配置重新加载"""
    print("\n🔄 测试配置重新加载:")
    
    # 重新加载单个配置
    config_manager.reload_config('app')
    
    # 重新加载所有配置
    reload_all_configs()

def test_error_handling():
    """测试错误处理"""
    print("\n⚠️ 测试错误处理:")
    
    # 测试无效配置名
    invalid_config = config_manager.get_config('invalid_config', 'some.key', '默认值')
    print(f"无效配置返回: {invalid_config}")
    
    # 测试无效键路径
    invalid_key = get_app_config('invalid.key.path', '默认值')
    print(f"无效键路径返回: {invalid_key}")

if __name__ == "__main__":
    print("🚀 开始测试配置管理器...")
    
    test_config_loading()
    test_specific_configs()
    test_nested_access()
    test_config_reload()
    test_error_handling()
    
    print("\n✅ 配置管理器测试完成！")
