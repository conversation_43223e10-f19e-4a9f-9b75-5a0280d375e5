"""测试process_result中任务可见性的时机问题"""

import asyncio
import time

TASK_NAME_PREFIX = "test_task_"
telegram_bot_task_name = f"{TASK_NAME_PREFIX}telegram_bot"
task_results = []

async def process_result(task):
    """模拟process_result的逻辑"""
    print(f"\n🔍 process_result被调用，处理任务: {task.get_name()}")
    
    task_ = task.result()
    task_results.append(task_)
    
    # 获取所有活跃任务
    active_tasks = asyncio.all_tasks()
    print(f"📊 当前所有活跃任务:")
    for t in active_tasks:
        print(f"  - {t.get_name()}: {t.done()}, {t.cancelled()}")
    
    # 过滤业务任务
    my_bizz_active_tasks = [
        task.get_name().replace(TASK_NAME_PREFIX, '') 
        for task in active_tasks 
        if (task.get_name() and 
            task.get_name() != telegram_bot_task_name and
            task.get_name().startswith(TASK_NAME_PREFIX))
    ]
    
    print(f"📋 过滤后的业务任务: {my_bizz_active_tasks}")
    
    stopped_task = task_["task_name"]
    if not my_bizz_active_tasks:
        msg = f"随着 [{stopped_task}] 的停止，没有任务在执行了"
        print(f"✅ {msg}")
    else:
        msg = f"虽然 [{stopped_task}] 停止，但还在执行: {', '.join(my_bizz_active_tasks)}"
        print(f"⏳ {msg}")

async def business_task(name, duration):
    """模拟业务任务"""
    try:
        print(f"🚀 {name} 开始运行")
        await asyncio.sleep(duration)
        print(f"✅ {name} 正常结束")
        return {"task_name": name, "task_result": "正常结束"}
    except asyncio.CancelledError:
        print(f"❌ {name} 被取消")
        return {"task_name": name, "task_result": "主动取消"}

async def background_task(name):
    """模拟后台任务"""
    try:
        print(f"🔄 {name} 开始运行")
        while True:
            await asyncio.sleep(1)
            print(f"💓 {name} 心跳")
    except asyncio.CancelledError:
        print(f"🛑 {name} 被取消")
        return  # 不返回结果

async def test_timing():
    """测试任务结束时机"""
    print("🧪 测试process_result中任务可见性...")
    
    # 创建任务
    tasks = []
    
    # 业务任务
    for i in range(3):
        task_name = f"business_{i+1}"
        task = asyncio.create_task(
            business_task(task_name, i + 1), 
            name=f"{TASK_NAME_PREFIX}{task_name}"
        )
        task.add_done_callback(lambda t: asyncio.create_task(process_result(t)))
        tasks.append(task)
    
    # 后台任务
    telegram_task = asyncio.create_task(
        background_task("telegram_bot"), 
        name=telegram_bot_task_name
    )
    memory_task = asyncio.create_task(
        background_task("memory_monitor"), 
        name=f"{TASK_NAME_PREFIX}memory_monitor"
    )
    
    tasks.extend([telegram_task, memory_task])
    
    # 等待业务任务完成
    await asyncio.sleep(5)
    
    print(f"\n🛑 开始取消所有任务...")
    for task in tasks:
        task.cancel()
    
    await asyncio.gather(*tasks, return_exceptions=True)
    print(f"\n✅ 所有任务已结束")

if __name__ == "__main__":
    asyncio.run(test_timing())
