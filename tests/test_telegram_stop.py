#!/usr/bin/env python3
"""测试Telegram停止功能的脚本"""

import asyncio
import signal
import os
import sys

async def test_event_based_stop():
    """测试基于asyncio.Event的停止机制"""
    print("🧪 测试基于asyncio.Event的停止机制")

    # 创建停止事件
    stop_event = asyncio.Event()

    async def mock_main_program():
        """模拟主程序"""
        try:
            print("📱 模拟程序开始运行...")
            print("💡 提示：按 Ctrl+C 或等待5秒后自动触发停止事件")

            # 模拟程序运行
            for i in range(50):
                if stop_event.is_set():
                    print("🛑 检测到停止事件，程序即将退出")
                    break
                print(f"⏰ 程序运行中... {i+1}/50")
                await asyncio.sleep(0.1)

            if not stop_event.is_set():
                print("✅ 程序正常完成")

        except asyncio.CancelledError:
            print("🛑 程序被取消")
            raise

    async def auto_stop_trigger():
        """5秒后自动触发停止"""
        await asyncio.sleep(5)
        print("⏰ 5秒到了，自动触发停止事件")
        stop_event.set()

    # 设置Ctrl+C处理
    def signal_handler(signum, frame):
        print("\n🛑 收到Ctrl+C信号，触发停止事件")
        stop_event.set()

    signal.signal(signal.SIGINT, signal_handler)

    try:
        # 并发运行主程序和自动停止触发器
        await asyncio.gather(
            mock_main_program(),
            auto_stop_trigger(),
            return_exceptions=True
        )
    except KeyboardInterrupt:
        print("🛑 键盘中断")

    print("✅ 测试完成")

def test_signal_based_stop():
    """测试基于系统信号的停止机制"""
    print("🧪 测试基于系统信号的停止机制")
    print("💡 这种方式直接使用系统信号，更加直接")

    def signal_handler(signum, frame):
        print(f"\n🛑 收到信号 {signum}，程序即将退出")
        print("✅ 优雅退出完成")
        sys.exit(0)

    # 注册信号处理器
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)

    try:
        print("📱 程序运行中，按 Ctrl+C 测试停止功能")
        for i in range(100):
            print(f"⏰ 运行中... {i+1}/100")
            import time
            time.sleep(0.1)
        print("✅ 程序正常完成")
    except KeyboardInterrupt:
        print("\n🛑 键盘中断")

def show_comparison():
    """显示不同方式的对比"""
    print("=" * 60)
    print("📊 不同停止方式对比")
    print("=" * 60)

    comparison = """
方式1: 文件标志 (原实现)
├── 优点: 简单易懂，跨进程通信
├── 缺点: 需要文件I/O，可能有延迟，不够优雅
└── 适用: 简单场景，调试用途

方式2: asyncio.Event (推荐)
├── 优点: 内存中操作，响应快速，代码优雅
├── 缺点: 仅限同一进程内通信
└── 适用: 单进程多任务场景

方式3: 系统信号
├── 优点: 标准做法，跨进程，系统级支持
├── 缺点: 平台相关性，信号处理复杂
└── 适用: 生产环境，多进程场景

方式4: 队列通信
├── 优点: 可传递复杂数据，支持多进程
├── 缺点: 相对复杂，需要额外管理
└── 适用: 复杂的进程间通信

方式5: 共享内存
├── 优点: 高性能，支持多进程
├── 缺点: 实现复杂，需要同步机制
└── 适用: 高性能要求的场景
"""
    print(comparison)

def main():
    print("=" * 50)
    print("🧪 Telegram停止功能测试脚本")
    print("=" * 50)
    print("1. 测试 asyncio.Event 方式 (推荐)")
    print("2. 测试系统信号方式")
    print("3. 查看不同方式对比")
    print("4. 退出")

    choice = input("请选择操作 (1-4): ").strip()

    if choice == '1':
        asyncio.run(test_event_based_stop())
    elif choice == '2':
        test_signal_based_stop()
    elif choice == '3':
        show_comparison()
    elif choice == '4':
        print("退出测试脚本")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
