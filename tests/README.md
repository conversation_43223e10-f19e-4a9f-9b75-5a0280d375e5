# 测试目录

这个目录包含了量化程序的所有测试文件。

## 📋 测试文件

- `test_restart.py` - 重启功能测试

## 🧪 运行测试

### 运行单个测试
```bash
python tests/test_restart.py
```

### 运行所有测试
```bash
# 使用pytest（推荐）
pytest tests/

# 或使用Python内置的unittest
python -m unittest discover tests/
```

## 📝 编写测试

### 测试文件命名规范
- 测试文件以 `test_` 开头
- 测试类以 `Test` 开头
- 测试方法以 `test_` 开头

### 测试目录结构
```
tests/
├── __init__.py
├── test_restart.py
├── test_monitor/
│   ├── __init__.py
│   ├── test_monitor1_volume.py
│   └── ...
├── test_trader/
│   ├── __init__.py
│   ├── test_trader1_ema.py
│   └── ...
└── test_utils/
    ├── __init__.py
    ├── test_logger.py
    └── ...
```

## 🔧 测试工具

推荐使用以下测试工具：
- `pytest` - 测试框架
- `unittest.mock` - 模拟对象
- `coverage` - 代码覆盖率
