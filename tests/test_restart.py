#!/usr/bin/env python3
"""测试重启功能的简单脚本"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到 sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.signal_handler import setup_signal_handlers, wait_for_stop_signal, get_signal_handler
from utils.utils_logger import setup_logger
from utils.utils_process_lock import ensure_single_instance

# 初始化日志
logger = setup_logger("test_restart", "test_restart.log")

async def test_main():
    """测试主函数"""
    logger.info(f"🚀 测试程序启动 - {datetime.now()}")

    # 确保只有一个实例在运行
    lock_file_path = os.path.join(os.path.dirname(__file__), "test_restart.lock")
    is_restart = os.getenv("QUANT_RESTART") == "1"
    ensure_single_instance(lock_file_path, "测试程序", wait_for_release=is_restart)

    # 设置信号处理器
    setup_signal_handlers(logger)
    
    # 模拟程序运行
    try:
        logger.info("📋 程序正在运行，发送 '/restart' 到Telegram测试重启功能")
        
        # 等待停止或重启信号
        result = await wait_for_stop_signal(logger)
        logger.info(f"📋 收到信号: {result}")
        
    except Exception as e:
        logger.error(f"程序异常: {e}")
    finally:
        logger.info("🔄 程序清理完成")

async def main_with_restart():
    """支持重启的主函数"""
    import subprocess
    
    while True:
        try:
            # 运行主程序
            await test_main()
            
            # 检查是否需要重启
            signal_handler = get_signal_handler()
            if signal_handler.is_restarting:
                logger.info("🔄 检测到重启信号，正在重启程序...")
                
                # 重启程序 - 保持原始启动方式
                python = sys.executable
                script = sys.argv[0]
                args = sys.argv[1:]

                # 检查是否有自定义的启动命令（通过环境变量）
                original_start_cmd = os.getenv('QUANT_START_CMD', '')

                if original_start_cmd:
                    # 使用原始启动命令
                    if 'caffeinate' in original_start_cmd:
                        restart_cmd = ['caffeinate', python, script] + args
                        logger.info(f"🔄 使用caffeinate重启: caffeinate {python} {script} {' '.join(args)}")
                    else:
                        restart_cmd = [python, script] + args
                        logger.info(f"🔄 重启命令: {python} {script} {' '.join(args)}")
                else:
                    # 默认启动方式
                    restart_cmd = [python, script] + args
                    logger.info(f"🔄 重启命令: {python} {script} {' '.join(args)}")

                # 使用延迟启动脚本来避免进程锁冲突
                restart_script = f"""
import time
import subprocess
import sys
import os

# 等待旧进程完全退出
time.sleep(3)

# 设置重启标识环境变量
env = os.environ.copy()
env['QUANT_RESTART'] = '1'

# 启动新进程
subprocess.Popen({restart_cmd}, env=env)
"""

                # 创建临时重启脚本
                import tempfile
                with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                    f.write(restart_script)
                    restart_script_path = f.name

                # 启动延迟重启脚本
                subprocess.Popen([python, restart_script_path],
                               stdout=subprocess.DEVNULL,
                               stderr=subprocess.DEVNULL)

                logger.info("✅ 延迟重启脚本已启动，当前进程即将退出")
                break
            else:
                # 正常退出，不重启
                logger.info("✅ 程序正常退出")
                break
                
        except Exception as e:
            logger.error(f"程序运行异常: {e}")
            break

if __name__ == "__main__":
    print("🧪 重启功能测试程序")
    print("📱 请在Telegram中发送 '/restart' 或 '重启程序' 来测试重启功能")
    print("📱 发送 '/stop' 或 '停止程序' 来正常退出")
    print("⌨️  或者按 Ctrl+C 来强制退出")
    print("-" * 50)
    
    # 运行支持重启的事件循环
    asyncio.run(main_with_restart())
