"""测试Exchange安全机制"""

import asyncio
import sys
import os

# 添加项目根目录到 sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.exchange_wrapper import create_exchange_wrapper

async def test_forbidden_methods():
    """测试禁止方法的安全机制"""
    print("🧪 测试Exchange安全机制...")
    
    # 创建exchange包装器
    exchange = create_exchange_wrapper("test_security")
    
    # 测试禁止的方法
    forbidden_methods = [
        'close',
        'set_sandbox_mode', 
        'set_proxy',
        'set_headers',
        'set_timeout',
        'set_api_key',
        'set_secret',
    ]
    
    for method_name in forbidden_methods:
        try:
            print(f"\n📋 测试禁止方法: {method_name}")
            method = getattr(exchange, method_name)
            
            if method_name == 'close':
                # close是async方法
                await method()
            else:
                # 其他方法可能是sync或async
                try:
                    await method()
                except TypeError:
                    # 如果不是async方法，直接调用
                    method()
                    
        except PermissionError as e:
            print(f"✅ 安全机制生效: {e}")
        except Exception as e:
            print(f"❌ 意外错误: {e}")
    
    print(f"\n🎯 安全测试完成！")

async def test_allowed_methods():
    """测试允许的方法"""
    print(f"\n🧪 测试允许的方法...")
    
    exchange = create_exchange_wrapper("test_allowed")
    
    try:
        # 测试load_markets（特殊处理）
        print("📋 测试 load_markets...")
        markets = await exchange.load_markets()
        print(f"✅ load_markets 工作正常，返回类型: {type(markets)}")
        
        # 测试其他安全方法（这些需要真实的exchange连接，这里只测试方法存在性）
        safe_methods = ['fetch_time', 'fetch_ohlcv', 'fetch_order_book']
        for method_name in safe_methods:
            method = getattr(exchange, method_name)
            print(f"✅ {method_name} 方法可访问: {callable(method)}")
            
    except Exception as e:
        print(f"⚠️ 测试允许方法时出错（可能是因为没有真实连接）: {e}")

if __name__ == "__main__":
    asyncio.run(test_forbidden_methods())
    asyncio.run(test_allowed_methods())
