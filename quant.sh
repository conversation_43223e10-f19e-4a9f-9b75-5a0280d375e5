#!/bin/bash

# CorsairQuant程序管理脚本 - 一站式管理工具
# 使用方法：
#   ./quant.sh start [caffeinate]  # 启动程序
#   ./quant.sh stop [force]        # 停止程序
#   ./quant.sh restart [caffeinate] # 重启程序
#   ./quant.sh status              # 查看状态
#   ./quant.sh logs                # 查看日志
#   ./quant.sh tail                # 实时日志

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"
cd "$SCRIPT_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示Logo（调用专门的logo脚本）
show_logo() {
    python3 "$PROJECT_ROOT/scripts/show_logo.py" 2>/dev/null || {
        # 如果Python调用失败，显示简化版本
        echo -e "${CYAN}"
        echo "    ⚔️  ═══════════════════════════════════════ ⚔️"
        echo "       ██████  ██████  ██████  ███████  █████  ██ ██████  "
        echo "      ██      ██    ██ ██   ██ ██      ██   ██ ██ ██   ██ "
        echo "      ██      ██    ██ ██████  ███████ ███████ ██ ██████  "
        echo "      ██      ██    ██ ██   ██      ██ ██   ██ ██ ██   ██ "
        echo "       ██████  ██████  ██   ██ ███████ ██   ██ ██ ██   ██ "
        echo "                                                          "
        echo "    🏴‍☠️                    QUANT                     ⚓"
        echo "                                                          "
        echo "         ⛵ ～～～～～～～～～～～～～～～～～～～～～ ⛵"
        echo -e "${NC}"
        echo -e "${BLUE}           CorsairQuant 量化交易程序管理工具 v1.0${NC}"
        echo "    ═══════════════════════════════════════════════════"
    }
}

# 显示帮助
show_help() {
    show_logo
    echo -e "${YELLOW}使用方法:${NC}"
    echo ""
    echo -e "${GREEN}启动相关:${NC}"
    echo -e "  ${CYAN}./quant.sh start${NC}              # 普通启动"
    echo -e "  ${CYAN}./quant.sh start caffeinate${NC}   # 使用caffeinate启动(防休眠)"
    echo ""
    echo -e "${GREEN}停止相关:${NC}"
    echo -e "  ${CYAN}./quant.sh stop${NC}               # 优雅停止"
    echo -e "  ${CYAN}./quant.sh stop force${NC}         # 强制停止"
    echo -e "  ${CYAN}./quant.sh stop telegram${NC}      # 通过Telegram停止"
    echo ""
    echo -e "${GREEN}重启相关:${NC}"
    echo -e "  ${CYAN}./quant.sh restart${NC}            # 重启程序"
    echo -e "  ${CYAN}./quant.sh restart caffeinate${NC} # 使用caffeinate重启"
    echo ""
    echo -e "${GREEN}状态查询:${NC}"
    echo -e "  ${CYAN}./quant.sh status${NC}             # 查看程序状态"
    echo -e "  ${CYAN}./quant.sh ps${NC}                 # 查看相关进程"
    echo -e "  ${CYAN}./quant.sh logs${NC}               # 查看最新日志"
    echo -e "  ${CYAN}./quant.sh tail${NC}               # 实时查看日志"
    echo ""
    echo -e "${GREEN}配置管理:${NC}"
    echo -e "  ${CYAN}./quant.sh reload${NC}             # 重载所有配置"
    echo -e "  ${CYAN}./quant.sh reload app${NC}         # 重载应用配置"
    echo -e "  ${CYAN}./quant.sh reload monitor${NC}     # 重载监控配置"
    echo -e "  ${CYAN}./quant.sh reload exchange${NC}    # 重载交易所配置"
    echo -e "  ${CYAN}./quant.sh reload telegram${NC}    # 重载Telegram配置"
    echo -e "  ${CYAN}./quant.sh reload trader${NC}      # 重载交易配置"
    echo -e "  ${CYAN}./quant.sh config${NC}             # 显示当前配置"
    echo -e "  ${CYAN}./quant.sh config --detailed${NC}  # 显示详细配置"
    echo ""
    echo -e "${GREEN}其他:${NC}"
    echo -e "  ${CYAN}./quant.sh help${NC}               # 显示帮助"
    echo ""
    echo -e "${YELLOW}Telegram远程控制:${NC}"
    echo -e "  发送 ${CYAN}/start${NC} 或 ${CYAN}启动程序${NC} - 启动程序"
    echo -e "  发送 ${CYAN}/stop${NC} 或 ${CYAN}停止程序${NC} - 停止程序"
    echo -e "  发送 ${CYAN}/restart${NC} 或 ${CYAN}重启程序${NC} - 重启程序"
    echo -e "  发送 ${CYAN}/status${NC} 或 ${CYAN}状态${NC} - 查询状态"
}

# 启动程序
start_program() {
    echo -e "${BLUE}🚀 启动CorsairQuant程序...${NC}"

    if [ "$1" = "caffeinate" ]; then
        ./scripts/start_quant.sh caffeinate
    else
        ./scripts/start_quant.sh
    fi
}

# 停止程序
stop_program() {
    echo -e "${BLUE}🛑 停止CorsairQuant程序...${NC}"

    case "$1" in
        "force")
            ./scripts/stop_quant.sh force
            ;;
        "telegram")
            ./scripts/stop_quant.sh telegram
            ;;
        *)
            ./scripts/stop_quant.sh
            ;;
    esac
}

# 重启程序
restart_program() {
    echo -e "${BLUE}🔄 重启CorsairQuant程序...${NC}"

    # 使用新的重启脚本，确保资源正确清理
    if [ "$1" = "caffeinate" ]; then
        echo -e "${BLUE}🔄 使用caffeinate重启...${NC}"
        python3 ./scripts/restart_quant.py --caffeinate
    else
        echo -e "${BLUE}🔄 正常重启...${NC}"
        python3 ./scripts/restart_quant.py
    fi

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 程序重启成功${NC}"
    else
        echo -e "${RED}❌ 程序重启失败${NC}"
    fi
}

# 查看状态
show_status() {
    ./scripts/status_quant.sh
}

# 查看日志
show_logs() {
    ./scripts/status_quant.sh logs
}

# 实时日志
tail_logs() {
    ./scripts/status_quant.sh tail
}

# 查看相关进程
show_processes() {
    echo -e "${BLUE}🔍 查看CorsairQuant程序相关进程${NC}"
    echo "=================================="

    # 使用您提供的精确命令
    local processes=$(ps aux | grep python | grep -E "(corsair_engine\.py|CorsairQuant)" | grep -v grep)

    if [ -n "$processes" ]; then
        echo -e "${GREEN}找到以下相关进程:${NC}"
        echo ""
        # 显示表头
        echo -e "${CYAN}USER       PID  %CPU %MEM    VSZ   RSS TTY      STAT START   TIME COMMAND${NC}"
        echo "$processes"
    else
        echo -e "${YELLOW}⚠️  未找到相关的Python进程${NC}"
        echo ""
        echo -e "${BLUE}💡 提示: 如果程序正在运行但未显示，可能是因为:${NC}"
        echo -e "  • 进程名称不包含 'corsair_engine.py' 或 'CorsairQuant'"
        echo -e "  • 程序使用了不同的启动方式"
        echo ""
        echo -e "${BLUE}🔍 您也可以手动执行以下命令查看所有Python进程:${NC}"
        echo -e "  ${CYAN}ps aux | grep python${NC}"
    fi

    echo ""
    echo -e "${BLUE}📋 完整命令: ${CYAN}ps aux | grep python | grep -E \"(corsair_engine\.py|CorsairQuant)\" | grep -v grep${NC}"
}

# 配置重载功能
reload_config() {
    local config_type="${1:-all}"
    local show_config=false
    local no_reload=false

    # 验证配置类型
    case "$config_type" in
        all|app|monitor|exchange|telegram|trader)
            # 有效的配置类型
            ;;
        -s|--show)
            show_config=true
            config_type="all"
            ;;
        --no-reload)
            no_reload=true
            config_type="all"
            ;;
        *)
            echo -e "${RED}❌ 未知配置类型: $config_type${NC}"
            echo "支持的配置类型: all, app, monitor, exchange, telegram, trader"
            return 1
            ;;
    esac

    # 解析额外参数
    shift
    while [[ $# -gt 0 ]]; do
        case $1 in
            -s|--show)
                show_config=true
                shift
                ;;
            --no-reload)
                no_reload=true
                shift
                ;;
            *)
                echo -e "${RED}❌ 未知参数: $1${NC}"
                echo "支持的参数: -s/--show, --no-reload"
                return 1
                ;;
        esac
    done

    # 检查重载脚本是否存在
    local reload_script="$PROJECT_ROOT/scripts/reload_config.py"
    if [ ! -f "$reload_script" ]; then
        echo -e "${RED}❌ 重载脚本不存在: $reload_script${NC}"
        return 1
    fi

    # 构建Python命令
    local python_cmd="python3 $reload_script -t $config_type"

    if [ "$show_config" = true ]; then
        python_cmd="$python_cmd -s"
    fi

    if [ "$no_reload" = true ]; then
        python_cmd="$python_cmd --no-reload"
    fi

    # 显示执行信息
    echo -e "${BLUE}🔄 执行配置重载...${NC}"
    echo -e "${YELLOW}配置类型: $config_type${NC}"
    echo ""

    # 执行命令
    if eval "$python_cmd"; then
        echo ""
        echo -e "${GREEN}✅ 配置重载完成${NC}"
    else
        echo ""
        echo -e "${RED}❌ 配置重载失败${NC}"
        return 1
    fi
}

# 显示当前配置
show_config() {
    local detailed_flag=""

    # 检查是否有 --detailed 参数
    for arg in "$@"; do
        if [[ "$arg" == "--detailed" || "$arg" == "-d" ]]; then
            detailed_flag="--detailed"
            break
        fi
    done

    if [[ -n "$detailed_flag" ]]; then
        echo -e "${BLUE}📋 显示详细配置...${NC}"
    else
        echo -e "${BLUE}📋 显示当前配置...${NC}"
    fi

    # 检查重载脚本是否存在
    local reload_script="$PROJECT_ROOT/scripts/reload_config.py"
    if [ ! -f "$reload_script" ]; then
        echo -e "${RED}❌ 配置脚本不存在: $reload_script${NC}"
        return 1
    fi

    # 直接调用Python脚本显示配置，不显示重载相关信息
    local python_cmd="python3 $reload_script -t all -s --no-reload $detailed_flag"

    # 执行命令
    if eval "$python_cmd"; then
        echo ""
        echo -e "${GREEN}✅ 配置显示完成${NC}"
    else
        echo ""
        echo -e "${RED}❌ 配置显示失败${NC}"
        return 1
    fi
}

# 主逻辑
case "$1" in
    "start")
        start_program "$2"
        ;;
    "stop")
        stop_program "$2"
        ;;
    "restart")
        restart_program "$2"
        ;;
    "status")
        show_status
        ;;
    "ps")
        show_processes
        ;;
    "logs")
        show_logs
        ;;
    "tail")
        tail_logs
        ;;
    "reload")
        shift  # 移除 "reload" 参数
        reload_config "$@"  # 传递所有剩余参数
        ;;
    "config")
        show_config
        ;;
    "help"|"-h"|"--help"|"")
        show_help
        ;;
    *)
        echo -e "${RED}❌ 未知命令: $1${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac
