# 工具脚本目录

这个目录包含了量化程序的各种工具和实用脚本。

## 📋 工具列表

### 🚨 故障恢复工具
- `cleanup_and_restart.py` - 故障恢复和强制清理工具
  - **⚠️ 这是故障恢复工具，日常请使用 `./quant.sh`**
  - 强制停止所有相关进程（包括僵尸进程）
  - 清理所有锁文件
  - 清理过大的日志文件（>50MB）
  - 完全重置运行环境
  - **智能重启** - 自动检测原始启动方式（caffeinate/python）
  - 优先使用脚本系统重启，保持启动方式一致性
  - 适用于正常脚本无法解决的问题

### 📊 分析工具
- `analyze_initial_memory.py` - 内存分析工具
  - 分析程序启动时的内存占用
  - 分析各个模块的内存消耗
  - 帮助优化内存使用

## 🚀 使用方法

### 故障恢复工具
```bash
# ⚠️ 故障恢复工具 - 仅在正常脚本无法解决问题时使用
# 日常使用请优先: ./quant.sh start/stop/restart

# 交互式故障恢复
python tools/cleanup_and_restart.py

# 故障恢复后自动重启
python tools/cleanup_and_restart.py --restart

# 赋予执行权限后直接运行
chmod +x tools/cleanup_and_restart.py
./tools/cleanup_and_restart.py
```

### 内存分析工具
```bash
# 完整分析
python tools/analyze_initial_memory.py

# 只分析基础导入
python tools/analyze_initial_memory.py --basic

# 只分析corsair_engine.py导入
python tools/analyze_initial_memory.py --main

# 只分析Telegram导入
python tools/analyze_initial_memory.py --telegram

# 查看帮助
python tools/analyze_initial_memory.py --help
```

## ⚠️ 注意事项

1. **清理工具使用注意**：
   - 会停止所有相关的Python进程
   - 会清理锁文件和大日志文件
   - 使用前请确保保存重要数据

2. **路径要求**：
   - 所有工具都需要从项目根目录运行
   - 工具会自动处理相对路径

3. **权限要求**：
   - 清理工具可能需要进程管理权限
   - 确保有足够权限操作文件和进程

## 🔧 开发说明

这些工具主要用于：
- 开发调试时的程序管理
- 生产环境的维护操作
- 性能分析和优化
- 故障排除和恢复
