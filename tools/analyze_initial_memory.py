"""分析程序初始内存占用"""

import sys
import os
import psutil
import importlib
import argparse
from pathlib import Path

# 添加项目根目录到 sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def get_memory_mb():
    """获取当前内存使用量(MB)"""
    return psutil.Process().memory_info().rss / 1024 / 1024

def analyze_import_memory():
    """分析各个导入模块的内存占用"""
    print("🔍 分析程序初始内存占用...")
    
    # 基础内存（Python解释器 + 基础模块）
    base_memory = get_memory_mb()
    print(f"📊 Python解释器基础内存: {base_memory:.2f} MB")
    
    # 分析各个导入的内存增长
    imports_to_test = [
        ("asyncio", "import asyncio"),
        ("os", "import os"),
        ("traceback", "import traceback"),
        ("dotenv", "from dotenv import load_dotenv"),
        ("re", "import re"),
        ("psutil", "import psutil"),
        ("telegram", "from telegram.ext import MessageHandler, filters"),
        ("pandas", "import pandas as pd"),
        ("numpy", "import numpy as np"),
        ("ccxt", "import ccxt"),
        ("ccxt.pro", "import ccxt.pro as ccxtpro"),
    ]
    
    memory_before = get_memory_mb()
    memory_breakdown = []
    
    for name, import_statement in imports_to_test:
        try:
            memory_before_import = get_memory_mb()
            exec(import_statement)
            memory_after_import = get_memory_mb()
            memory_increase = memory_after_import - memory_before_import
            
            if memory_increase > 0.1:  # 只显示增长超过0.1MB的
                memory_breakdown.append((name, memory_increase))
                print(f"  📦 {name}: +{memory_increase:.2f} MB")
            
        except ImportError:
            print(f"  ❌ {name}: 未安装")
        except Exception as e:
            print(f"  ⚠️ {name}: 导入失败 - {e}")
    
    total_imports_memory = sum(increase for _, increase in memory_breakdown)
    current_memory = get_memory_mb()
    
    print(f"\n📊 内存占用分析:")
    print(f"  Python解释器基础: {base_memory:.2f} MB")
    print(f"  主要导入模块总计: {total_imports_memory:.2f} MB")
    print(f"  当前总内存: {current_memory:.2f} MB")
    print(f"  其他开销: {current_memory - base_memory - total_imports_memory:.2f} MB")
    
    return memory_breakdown

def analyze_main_imports():
    """分析main.py中实际导入的模块"""
    print(f"\n🔍 分析main.py实际导入的模块...")
    
    # 模拟main.py的导入顺序
    main_imports = [
        ("asyncio", "import asyncio"),
        ("os", "import os"), 
        ("traceback", "import traceback"),
        ("dotenv", "from dotenv import load_dotenv"),
        ("re", "import re"),
        ("utils.utils_logger", "from utils.utils_logger import setup_logger"),
        ("errors", "from errors import CustomError, ErrorCode"),
        ("constants", "from constants import TASK_NAME_PREFIX"),
        ("utils.utils_telegram", "from utils.utils_telegram import send_telegram_message, send_telegram_message_without_prefix, init_telegram_bot, reply_to_message, wait_for_message_queue,set_program_start_time"),
        ("utils.utils_process_lock", "from utils.utils_process_lock import ensure_single_instance"),
        ("telegram.ext", "from telegram.ext import MessageHandler, filters"),
        ("utils.utils_common", "from utils.utils_common import print_ctrl_c_newline"),
        ("utils.resource_monitor", "from utils.resource_monitor import print_resource_report, resource_monitor"),
        ("utils.exchange_manager", "from utils.exchange_manager import close_global_exchange"),
    ]
    
    memory_before = get_memory_mb()
    print(f"📊 导入前内存: {memory_before:.2f} MB")
    
    total_increase = 0
    for name, import_statement in main_imports:
        try:
            memory_before_import = get_memory_mb()
            exec(import_statement)
            memory_after_import = get_memory_mb()
            memory_increase = memory_after_import - memory_before_import
            total_increase += memory_increase
            
            if memory_increase > 0.5:  # 只显示增长超过0.5MB的重要模块
                print(f"  📦 {name}: +{memory_increase:.2f} MB")
                
        except Exception as e:
            print(f"  ❌ {name}: 导入失败 - {e}")
    
    final_memory = get_memory_mb()
    print(f"\n📊 main.py导入后:")
    print(f"  总内存增长: +{total_increase:.2f} MB")
    print(f"  最终内存: {final_memory:.2f} MB")

def analyze_telegram_memory():
    """专门分析Telegram库的内存占用"""
    print(f"\n🔍 分析Telegram库内存占用...")
    
    memory_before = get_memory_mb()
    print(f"📊 导入前: {memory_before:.2f} MB")
    
    try:
        # 逐步导入telegram相关模块
        import telegram
        memory_after_telegram = get_memory_mb()
        print(f"  📦 import telegram: +{memory_after_telegram - memory_before:.2f} MB")
        
        from telegram.ext import Application
        memory_after_ext = get_memory_mb()
        print(f"  📦 telegram.ext: +{memory_after_ext - memory_after_telegram:.2f} MB")
        
        from telegram.ext import MessageHandler, filters
        memory_after_handlers = get_memory_mb()
        print(f"  📦 handlers & filters: +{memory_after_handlers - memory_after_ext:.2f} MB")
        
        total_telegram = memory_after_handlers - memory_before
        print(f"  📊 Telegram总计: +{total_telegram:.2f} MB")
        
    except Exception as e:
        print(f"  ❌ Telegram导入失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="分析量化程序内存占用")
    parser.add_argument("--basic", action="store_true", help="只分析基础导入")
    parser.add_argument("--main", action="store_true", help="只分析corsair_engine.py导入")
    parser.add_argument("--telegram", action="store_true", help="只分析Telegram导入")

    args = parser.parse_args()

    print("🚀 开始分析初始内存占用...")

    if args.basic or not any([args.basic, args.main, args.telegram]):
        # 分析基础导入
        analyze_import_memory()

    if args.main or not any([args.basic, args.main, args.telegram]):
        # 分析corsair_engine.py实际导入
        analyze_main_imports()

    if args.telegram or not any([args.basic, args.main, args.telegram]):
        # 专门分析Telegram
        analyze_telegram_memory()

    print(f"\n✅ 分析完成！")

if __name__ == "__main__":
    main()
