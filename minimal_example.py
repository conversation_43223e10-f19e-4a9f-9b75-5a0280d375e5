"""示例：最小的可执行文件：用于验证环境、接口调试等"""
# 暂未包含重试机制

import sys
import ccxt
import ccxt.pro as ccxtpro
from pprint import pprint
import os
import asyncio
import traceback
from dotenv import load_dotenv

# 添加项目根目录到 sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.utils_logger import setup_logger
from utils.utils_logger import log_block
from errors import CustomError, ErrorCode
from utils.utils_telegram import send_telegram_message
from constants import TASK_DIVIDER

print('CCXT Version:', ccxt.__version__)
print('CCXT pro Version:', ccxtpro.__version__)

# 1、打印ccxt支持的交易所
# print(ccxt.exchanges)
# for exchange_id in ccxt.exchanges:
#    try:
#        exchange = getattr(ccxt, exchange_id)()
#        print(exchange_id)
#        # do what you want with this exchange
#        # pprint(dir(exchange))
#    except Exception as e:
#        print(e) 
# print('-----------------------------------')

# 1、初始化日志记录器
current_file_name = os.path.splitext(os.path.basename(__file__))[0]
log_name = f"{current_file_name}_logger"
log_file_name = f"{current_file_name}.log"
logger = setup_logger(log_name=log_name,log_file=log_file_name)

# 2、加载.env文件, 读取apiKey和secret 【已经添加.env到.gitignore中，所以不会被提交到git中，也就不会泄露】
load_dotenv()
API_KEY = os.getenv("API_KEY")
API_SECRET = os.getenv("API_SECRET")

# 3、创建某个交易所，以 Binance 交易所实例
# exchange = ccxt.binance({   # 同步的方式，只能调用同步接口
exchange = ccxtpro.binance({
    'apiKey': API_KEY,
    'secret': API_SECRET,
    'https_proxy': 'http://127.0.0.1:7890',
    'ws_proxy':' http://127.0.0.1:7890',
})
# pprint(dir(exchange))

async def main():

    logger.info("程序开始运行！")
    # # send_email(f"{current_file_name} 开始运行", "启动啦")
    # # 发送Telegram消息
    # await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"开始运行！")

    try:

        # 4、测试代理是否有效
        print("测试代理是否有效:")
        print(await exchange.fetch('https://api.ipify.org/'))

        # 5、设置沙箱模式（测试环境）
        # exchange.set_sandbox_mode(True)

        # 6、手动load_markets，可以不用手动load
        # try:
        #     markets = await exchange.load_markets()
        #     # print(markets)
        #     print('--------------')
        #     # print(list(exchange.markets.keys()))
        # except Exception as e:
        #     print(f'Error: {e}')

        # 7、打印所需的权限
        # print(exchange.requiredCredentials)
        # # 测试是否有所需的权限
        # print(exchange.check_required_credentials()) 

        # 一些合约相关的symbol
        # BTC/USDT:BTC（反向永续合约）以 BTC 进行结算
        # BTC/USDT:USDT（线性永续合约）以 USDT 进行结算

        # 8、业务接口调用：常规响应
        # print("fetch_tickers:")
        # print(await exchange.fetch_tickers(['ETH/BTC', 'LTC/BTC']))
        # print("fetch_ohlcv:")
        # print(await exchange.fetch_ohlcv('ONDO/USDT:USDT','5m', limit=5))
        # print("fetch_trades:")
        # print(await exchange.fetch_trades('BTC/USDT:USDT',limit=5))
        # print("fetch_open_orders:")
        # print(await exchange.fetch_open_orders('AVAX/USDT:USDT'))
        # print("fetch_order_book:")
        # print(await exchange.fetch_order_book('AVAX/USDT:USDT',5))
        # print("fetch_positions:")
        # print(await exchange.fetch_positions())

        # 9、业务接口调用：流式响应
        # while True:
        #     print("watch_order_book:")
        #     print(await exchange.watch_order_book('AVAX/USDT:USDT',5))

        while True:
            print("watch_orders")
            print(await exchange.watch_orders('ONDO/USDT:USDT'))



    except CustomError as e:
        reason = f"{str(e)}"
    except asyncio.CancelledError:
        # 任务被取消
        print()
        reason = f"主动取消"
    except Exception as e:
        logger.error(f"报错: {traceback.format_exc()}")
        reason = f"报错: {repr(e)}"
    finally:
        # 显示 `reason` 的默认值，以防止未设置导致的问题
        if 'reason' not in locals():  # 判断 reason 是否被赋值
            reason = "执行结束"
        # # send_email(f"{current_file_name} 停止运行", f"{reason}")
        # # 发送Telegram消息
        # await send_telegram_message(TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID, current_file_name, f"停止运行！原因:\n{reason}")
        # exchange由全局管理，无需在此处关闭
        # await exchange.close()  # 确保资源被释放
        logger.info(f"程序停止运行！原因: {reason}")
        return {"task_name":current_file_name, "task_result":reason}

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        # 任务被取消，输出换行符避免与^C共行
        print()
        logger.info(f"主动取消")
    finally:
        # 程序级清理（关闭exchange）- 只在独立运行时执行
        # 注意：minimal_example使用独立的exchange实例，所以需要关闭
        async def cleanup_exchange():
            try:
                await exchange.close()
                print("✅ Exchange连接已关闭")
            except Exception as e:
                print(f"Exchange关闭时出错: {e}")

        # 在新的事件循环中关闭exchange
        try:
            asyncio.run(cleanup_exchange())
        except Exception:
            pass  # 忽略清理时的错误